<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>预览页面滚动条修复测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        html, body {
            height: 100%;
            overflow-x: hidden;
        }
        
        .test-container {
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        
        .test-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }
        
        .test-description {
            color: #666;
            margin-bottom: 15px;
        }
        
        .responsive-demo {
            width: 100vw;
            height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            font-weight: bold;
            position: fixed;
            top: 0;
            left: 0;
            z-index: 1000;
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            overflow: hidden;
        }
        
        .demo-button {
            padding: 10px 20px;
            background: #409eff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        
        .demo-button:hover {
            background: #66b1ff;
        }
        
        .hidden {
            display: none;
        }
        
        .status-indicator {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-weight: bold;
        }
        
        .status-good {
            background: #f0f9ff;
            color: #0369a1;
            border: 1px solid #7dd3fc;
        }
        
        .status-bad {
            background: #fef2f2;
            color: #dc2626;
            border: 1px solid #fca5a5;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>预览页面滚动条修复验证</h1>
        
        <div class="test-section">
            <div class="test-title">1. 滚动条检测</div>
            <div class="test-description">
                检查当前页面是否有滚动条
            </div>
            <div id="scrollStatus" class="status-indicator">检测中...</div>
            <button class="demo-button" onclick="checkScrollbars()">重新检测</button>
        </div>

        <div class="test-section">
            <div class="test-title">2. 响应式全屏演示</div>
            <div class="test-description">
                模拟响应式模式的全屏效果，检查是否产生滚动条
            </div>
            <button class="demo-button" onclick="showResponsiveDemo()">显示全屏演示</button>
            <button class="demo-button" onclick="hideResponsiveDemo()">隐藏演示</button>
        </div>

        <div class="test-section">
            <div class="test-title">3. 修复说明</div>
            <div class="test-description">
                <h4>修复的问题：</h4>
                <ul>
                    <li>响应式模式下预览页面出现横向和纵向滚动条</li>
                    <li>100vw和100vh与容器padding冲突</li>
                </ul>
                
                <h4>修复方案：</h4>
                <ul>
                    <li>响应式模式下移除容器padding</li>
                    <li>使用position: fixed确保完全占满屏幕</li>
                    <li>添加overflow: hidden防止滚动条</li>
                    <li>使用box-sizing: border-box确保尺寸计算正确</li>
                </ul>
            </div>
        </div>
    </div>

    <div id="responsiveDemo" class="responsive-demo hidden">
        <div style="text-align: center;">
            响应式模式演示<br>
            100vw × 100vh<br>
            <small style="opacity: 0.8;">应该没有滚动条</small><br>
            <button class="demo-button" onclick="hideResponsiveDemo()" style="margin-top: 20px;">关闭演示</button>
        </div>
    </div>

    <script>
        function checkScrollbars() {
            const hasHorizontalScrollbar = document.documentElement.scrollWidth > document.documentElement.clientWidth;
            const hasVerticalScrollbar = document.documentElement.scrollHeight > document.documentElement.clientHeight;
            
            const statusEl = document.getElementById('scrollStatus');
            
            if (!hasHorizontalScrollbar && !hasVerticalScrollbar) {
                statusEl.textContent = '✅ 无滚动条 - 修复成功';
                statusEl.className = 'status-indicator status-good';
            } else {
                let message = '❌ 检测到滚动条: ';
                if (hasHorizontalScrollbar) message += '横向 ';
                if (hasVerticalScrollbar) message += '纵向 ';
                statusEl.textContent = message;
                statusEl.className = 'status-indicator status-bad';
            }
        }

        function showResponsiveDemo() {
            document.getElementById('responsiveDemo').classList.remove('hidden');
            // 延迟检测，确保DOM更新完成
            setTimeout(checkScrollbars, 100);
        }

        function hideResponsiveDemo() {
            document.getElementById('responsiveDemo').classList.add('hidden');
            setTimeout(checkScrollbars, 100);
        }

        // 页面加载时自动检测
        window.addEventListener('load', checkScrollbars);
        window.addEventListener('resize', checkScrollbars);
    </script>
</body>
</html>
