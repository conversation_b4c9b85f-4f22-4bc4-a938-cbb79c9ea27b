<template>
  <div class="variable-panel">
    <div class="panel-header">
      <h3>变量管理</h3>
      <ElButton type="primary" size="small" @click="openVariableManager">
        <ElIcon><Setting /></ElIcon>
        变量管理
      </ElButton>
    </div>

    <div class="panel-content">
      <!-- 变量列表 -->
      <div class="variable-list">
        <div
          v-for="(value, key) in globalVariables"
          :key="key"
          class="variable-item"
        >
          <div class="variable-name">{{ key }}</div>
          <ElButton
            type="danger"
            size="small"
            :icon="Delete"
            @click="deleteVariable(key)"
          />
        </div>
        <div
          v-if="Object.keys(globalVariables).length === 0"
          class="empty-state"
        >
          暂无变量
        </div>
      </div>
    </div>

    <!-- 变量管理对话框 -->
    <ElDialog
      v-model="managerDialogVisible"
      title="变量管理"
      width="800px"
      :before-close="handleManagerClose"
    >
      <div class="variable-manager">
        <div class="manager-header">
          <ElButton type="primary" @click="addNewVariable">
            <ElIcon><Plus /></ElIcon>
            添加变量
          </ElButton>
        </div>

        <ElTable :data="variableList" border style="width: 100%">
          <ElTableColumn label="变量名" width="150">
            <template #default="{ row }">
              <ElInput
                v-model="row.name"
                size="small"
                placeholder="变量名"
                @change="validateVariableName(row)"
              />
            </template>
          </ElTableColumn>

          <ElTableColumn label="类型" width="120">
            <template #default="{ row }">
              <ElSelect
                v-model="row.type"
                size="small"
                @change="handleTypeChange(row)"
              >
                <ElOption label="字符串" value="string" />
                <ElOption label="数字" value="number" />
                <ElOption label="布尔值" value="boolean" />
                <ElOption label="对象" value="object" />
                <ElOption label="数组" value="array" />
                <ElOption label="时间" value="date" />
                <ElOption label="时间段" value="dateRange" />
              </ElSelect>
            </template>
          </ElTableColumn>

          <ElTableColumn label="默认值" width="200">
            <template #default="{ row }">
              <ElInput
                v-if="row.type === 'string'"
                v-model="row.defaultValue"
                size="small"
                placeholder="默认值"
              />
              <ElInputNumber
                v-else-if="row.type === 'number'"
                v-model="row.defaultValue"
                size="small"
                style="width: 100%"
              />
              <ElSwitch
                v-else-if="row.type === 'boolean'"
                v-model="row.defaultValue"
              />
              <ElDatePicker
                v-else-if="row.type === 'date'"
                v-model="row.defaultValue"
                type="datetime"
                size="small"
                placeholder="选择时间"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                style="width: 100%"
              />
              <ElDatePicker
                v-else-if="row.type === 'dateRange'"
                v-model="row.defaultValue"
                type="datetimerange"
                size="small"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                style="width: 100%"
              />
              <ElButton
                v-else-if="row.type === 'object'"
                type="primary"
                size="small"
                @click="editObject(row)"
              >
                编辑对象
              </ElButton>
              <ElButton
                v-else-if="row.type === 'array'"
                type="primary"
                size="small"
                @click="editArray(row)"
              >
                编辑数组
              </ElButton>
              <ElInput
                v-else
                v-model="row.defaultValue"
                type="textarea"
                size="small"
                placeholder="JSON格式"
                :rows="2"
              />
            </template>
          </ElTableColumn>

          <ElTableColumn label="描述">
            <template #default="{ row }">
              <ElInput
                v-model="row.description"
                size="small"
                placeholder="变量描述"
              />
            </template>
          </ElTableColumn>

          <ElTableColumn label="操作" width="80">
            <template #default="{ $index }">
              <ElButton
                type="danger"
                size="small"
                :icon="Delete"
                @click="removeVariable($index)"
              />
            </template>
          </ElTableColumn>
        </ElTable>
      </div>

      <template #footer>
        <ElButton @click="cancelManager">取消</ElButton>
        <ElButton type="primary" @click="saveVariables">保存</ElButton>
      </template>
    </ElDialog>

    <!-- 数组编辑对话框 -->
    <ElDialog
      v-model="arrayDialogVisible"
      title="编辑数组"
      width="600px"
      :before-close="handleArrayDialogClose"
    >
      <div class="array-manager">
        <div class="array-header">
          <span>数组元素管理</span>
          <ElButton type="primary" size="small" @click="addArrayElement">
            <ElIcon><Plus /></ElIcon>
            添加元素
          </ElButton>
        </div>

        <ElTable :data="arrayElements" border style="width: 100%">
          <ElTableColumn label="索引" width="80">
            <template #default="{ $index }">
              {{ $index }}
            </template>
          </ElTableColumn>

          <ElTableColumn label="类型" width="120">
            <template #default="{ row }">
              <ElSelect
                v-model="row.type"
                size="small"
                @change="handleArrayElementTypeChange(row)"
              >
                <ElOption label="字符串" value="string" />
                <ElOption label="数字" value="number" />
                <ElOption label="布尔值" value="boolean" />
                <ElOption label="对象" value="object" />
                <ElOption label="时间" value="date" />
              </ElSelect>
            </template>
          </ElTableColumn>

          <ElTableColumn label="值">
            <template #default="{ row }">
              <ElInput
                v-if="row.type === 'string'"
                v-model="row.value"
                size="small"
                placeholder="元素值"
              />
              <ElInputNumber
                v-else-if="row.type === 'number'"
                v-model="row.value"
                size="small"
                style="width: 100%"
              />
              <ElSwitch
                v-else-if="row.type === 'boolean'"
                v-model="row.value"
              />
              <ElDatePicker
                v-else-if="row.type === 'date'"
                v-model="row.value"
                type="datetime"
                size="small"
                placeholder="选择时间"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                style="width: 100%"
              />
              <ElInput
                v-else
                v-model="row.value"
                type="textarea"
                size="small"
                placeholder="JSON格式"
                :rows="2"
              />
            </template>
          </ElTableColumn>

          <ElTableColumn label="操作" width="80">
            <template #default="{ $index }">
              <ElButton
                type="danger"
                size="small"
                :icon="Delete"
                @click="removeArrayElement($index)"
              />
            </template>
          </ElTableColumn>
        </ElTable>
      </div>

      <template #footer>
        <ElButton @click="cancelArrayEdit">取消</ElButton>
        <ElButton type="primary" @click="saveArrayEdit">保存</ElButton>
      </template>
    </ElDialog>

    <!-- 对象编辑对话框 -->
    <ElDialog
      v-model="objectDialogVisible"
      title="编辑对象"
      width="600px"
      :before-close="handleObjectDialogClose"
    >
      <div class="object-manager">
        <div class="object-header">
          <span>对象属性管理</span>
          <ElButton type="primary" size="small" @click="addObjectProperty">
            <ElIcon><Plus /></ElIcon>
            添加属性
          </ElButton>
        </div>

        <ElTable :data="objectProperties" border style="width: 100%">
          <ElTableColumn label="属性名" width="180">
            <template #default="{ row }">
              <ElInput
                v-model="row.key"
                size="small"
                placeholder="属性名"
                @change="validatePropertyKey(row)"
              />
            </template>
          </ElTableColumn>

          <ElTableColumn label="类型" width="120">
            <template #default="{ row }">
              <ElSelect
                v-model="row.type"
                size="small"
                @change="handlePropertyTypeChange(row)"
              >
                <ElOption label="字符串" value="string" />
                <ElOption label="数字" value="number" />
                <ElOption label="布尔值" value="boolean" />
                <ElOption label="数组" value="array" />
                <ElOption label="时间" value="date" />
              </ElSelect>
            </template>
          </ElTableColumn>

          <ElTableColumn label="值">
            <template #default="{ row }">
              <ElInput
                v-if="row.type === 'string'"
                v-model="row.value"
                size="small"
                placeholder="属性值"
              />
              <ElInputNumber
                v-else-if="row.type === 'number'"
                v-model="row.value"
                size="small"
                style="width: 100%"
              />
              <ElSwitch
                v-else-if="row.type === 'boolean'"
                v-model="row.value"
              />
              <ElDatePicker
                v-else-if="row.type === 'date'"
                v-model="row.value"
                type="datetime"
                size="small"
                placeholder="选择时间"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                style="width: 100%"
              />
              <ElInput
                v-else
                v-model="row.value"
                type="textarea"
                size="small"
                placeholder="JSON格式"
                :rows="2"
              />
            </template>
          </ElTableColumn>

          <ElTableColumn label="操作" width="80">
            <template #default="{ $index }">
              <ElButton
                type="danger"
                size="small"
                :icon="Delete"
                @click="removeObjectProperty($index)"
              />
            </template>
          </ElTableColumn>
        </ElTable>
      </div>

      <template #footer>
        <ElButton @click="cancelObjectEdit">取消</ElButton>
        <ElButton type="primary" @click="saveObjectEdit">保存</ElButton>
      </template>
    </ElDialog>
  </div>
</template>

<script setup>
  import { ref } from 'vue'
  import {
    ElButton,
    ElIcon,
    ElInput,
    ElInputNumber,
    ElSwitch,
    ElSelect,
    ElOption,
    ElDialog,
    ElTable,
    ElTableColumn,
    ElDatePicker,
    ElMessage
  } from 'element-plus'
  import { Plus, Delete, Setting } from '@element-plus/icons-vue'

  const props = defineProps({
    variables: {
      type: Object,
      default: () => ({})
    }
  })

  const emit = defineEmits(['update-variables'])

  const globalVariables = ref({ ...props.variables })
  const managerDialogVisible = ref(false)
  const variableList = ref([])
  const objectDialogVisible = ref(false)
  const objectProperties = ref([])
  const arrayDialogVisible = ref(false)
  const arrayElements = ref([])
  const currentEditingVariable = ref(null)

  // 获取变量类型
  const getVariableType = value => {
    if (Array.isArray(value)) {
      // 检查是否为时间段数组
      if (
        value.length === 2 &&
        value.every(v => typeof v === 'string' && /^\d{4}-\d{2}-\d{2}/.test(v))
      ) {
        return 'dateRange'
      }
      return 'array'
    }
    if (typeof value === 'string' && /^\d{4}-\d{2}-\d{2}/.test(value)) {
      return 'date'
    }
    if (typeof value === 'object' && value !== null) return 'object'
    return typeof value
  }

  // 获取默认值
  const getDefaultValueByType = type => {
    const defaultMap = {
      string: '',
      number: 0,
      boolean: false,
      object: {},
      array: [],
      date: '',
      dateRange: []
    }
    return defaultMap[type] || ''
  }

  // 打开变量管理对话框
  const openVariableManager = () => {
    // 将当前变量转换为列表格式
    variableList.value = Object.entries(globalVariables.value).map(
      ([key, value]) => ({
        name: key,
        type: getVariableType(value),
        defaultValue: value,
        description: '',
        originalName: key // 保存原始名称用于更新
      })
    )

    managerDialogVisible.value = true
  }

  // 添加新变量
  const addNewVariable = () => {
    variableList.value.push({
      name: '',
      type: 'string',
      defaultValue: '',
      description: '',
      originalName: null // 新变量没有原始名称
    })
  }

  // 删除变量
  const removeVariable = index => {
    variableList.value.splice(index, 1)
  }

  // 快速删除变量
  const deleteVariable = key => {
    delete globalVariables.value[key]
    emit('update-variables', globalVariables.value)
  }

  // 验证变量名
  const validateVariableName = row => {
    if (!row.name) {
      ElMessage.error('变量名不能为空')
      return false
    }
    if (!/^[a-zA-Z_][a-zA-Z0-9_]*$/.test(row.name)) {
      ElMessage.error('变量名只能包含字母、数字和下划线，且不能以数字开头')
      return false
    }
    return true
  }

  // 处理类型变更
  const handleTypeChange = row => {
    row.defaultValue = getDefaultValueByType(row.type)
  }

  // 保存变量
  const saveVariables = () => {
    // 验证所有变量
    const names = new Set()
    for (const variable of variableList.value) {
      if (!validateVariableName(variable)) {
        return
      }
      if (names.has(variable.name)) {
        ElMessage.error(`变量名 "${variable.name}" 重复`)
        return
      }
      names.add(variable.name)
    }

    // 构建新的变量对象
    const newVariables = {}
    for (const variable of variableList.value) {
      let value = variable.defaultValue

      // 处理不同类型的值
      try {
        if (variable.type === 'object' && typeof value === 'string') {
          value = JSON.parse(value || '{}')
        } else if (variable.type === 'array' && typeof value === 'string') {
          value = JSON.parse(value || '[]')
        }
      } catch {
        ElMessage.error(`变量 "${variable.name}" 的JSON格式错误`)
        return
      }

      newVariables[variable.name] = value
    }

    globalVariables.value = newVariables
    emit('update-variables', globalVariables.value)
    managerDialogVisible.value = false
    ElMessage.success('变量保存成功')
  }

  // 取消管理
  const cancelManager = () => {
    managerDialogVisible.value = false
  }

  // 关闭对话框前的处理
  const handleManagerClose = done => {
    done()
  }

  // 编辑数组
  const editArray = variable => {
    currentEditingVariable.value = variable

    // 将数组元素转换为可编辑的列表格式
    const arrayValue = variable.defaultValue || []
    arrayElements.value = arrayValue.map((value, index) => ({
      type: getVariableType(value),
      value,
      originalIndex: index
    }))

    arrayDialogVisible.value = true
  }

  // 添加数组元素
  const addArrayElement = () => {
    arrayElements.value.push({
      type: 'string',
      value: '',
      originalIndex: null
    })
  }

  // 删除数组元素
  const removeArrayElement = index => {
    arrayElements.value.splice(index, 1)
  }

  // 处理数组元素类型变更
  const handleArrayElementTypeChange = row => {
    const defaultValueMap = {
      string: '',
      number: 0,
      boolean: false,
      object: {},
      date: ''
    }
    row.value = defaultValueMap[row.type] || ''
  }

  // 保存数组编辑
  const saveArrayEdit = () => {
    // 构建数组
    const arrayValue = []
    for (const element of arrayElements.value) {
      let value = element.value

      // 处理对象类型
      if (element.type === 'object' && typeof value === 'string') {
        try {
          value = JSON.parse(value || '{}')
        } catch {
          ElMessage.error(`数组元素的JSON格式错误`)
          return
        }
      }

      arrayValue.push(value)
    }

    // 更新变量的默认值
    currentEditingVariable.value.defaultValue = arrayValue
    arrayDialogVisible.value = false
    ElMessage.success('数组编辑成功')
  }

  // 取消数组编辑
  const cancelArrayEdit = () => {
    arrayDialogVisible.value = false
  }

  // 数组编辑对话框关闭前处理
  const handleArrayDialogClose = done => {
    done()
  }

  // 编辑对象
  const editObject = variable => {
    currentEditingVariable.value = variable

    // 将对象属性转换为可编辑的列表格式
    const objectValue = variable.defaultValue || {}
    objectProperties.value = Object.entries(objectValue).map(
      ([key, value]) => ({
        key,
        type: getVariableType(value),
        value,
        originalKey: key
      })
    )

    objectDialogVisible.value = true
  }

  // 添加对象属性
  const addObjectProperty = () => {
    objectProperties.value.push({
      key: '',
      type: 'string',
      value: '',
      originalKey: null
    })
  }

  // 删除对象属性
  const removeObjectProperty = index => {
    objectProperties.value.splice(index, 1)
  }

  // 验证属性键名
  const validatePropertyKey = row => {
    if (!row.key) {
      ElMessage.error('属性名不能为空')
      return false
    }
    if (!/^[a-zA-Z_][a-zA-Z0-9_]*$/.test(row.key)) {
      ElMessage.error('属性名只能包含字母、数字和下划线，且不能以数字开头')
      return false
    }
    return true
  }

  // 处理属性类型变更
  const handlePropertyTypeChange = row => {
    const defaultValueMap = {
      string: '',
      number: 0,
      boolean: false,
      array: [],
      date: ''
    }
    row.value = defaultValueMap[row.type] || ''
  }

  // 保存对象编辑
  const saveObjectEdit = () => {
    // 验证所有属性
    const keys = new Set()
    for (const property of objectProperties.value) {
      if (!validatePropertyKey(property)) {
        return
      }
      if (keys.has(property.key)) {
        ElMessage.error(`属性名 "${property.key}" 重复`)
        return
      }
      keys.add(property.key)
    }

    // 构建对象
    const objectValue = {}
    for (const property of objectProperties.value) {
      let value = property.value

      // 处理数组类型
      if (property.type === 'array' && typeof value === 'string') {
        try {
          value = JSON.parse(value || '[]')
        } catch {
          ElMessage.error(`属性 "${property.key}" 的JSON格式错误`)
          return
        }
      }

      objectValue[property.key] = value
    }

    // 更新变量的默认值
    currentEditingVariable.value.defaultValue = objectValue
    objectDialogVisible.value = false
    ElMessage.success('对象编辑成功')
  }

  // 取消对象编辑
  const cancelObjectEdit = () => {
    objectDialogVisible.value = false
  }

  // 对象编辑对话框关闭前处理
  const handleObjectDialogClose = done => {
    done()
  }
</script>

<style scoped>
  .variable-panel {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .panel-header {
    padding: 16px;
    border-bottom: 1px solid #e4e7ed;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #fff;
  }

  .panel-header h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 500;
    color: #303133;
  }

  .panel-content {
    flex: 1;
    overflow-y: auto;
    padding: 16px;
  }

  .variable-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .variable-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 12px;
    border: 1px solid #e4e7ed;
    border-radius: 6px;
    background: #fff;
  }

  .variable-name {
    font-family: 'Monaco', 'Courier New', monospace;
    font-size: 13px;
    color: #606266;
    flex: 1;
  }

  .empty-state {
    text-align: center;
    color: #909399;
    padding: 40px 0;
    font-size: 14px;
  }

  .variable-manager {
    padding: 16px 0;
  }

  .manager-header {
    margin-bottom: 16px;
    display: flex;
    justify-content: flex-end;
  }

  :deep(.el-table .el-input__inner) {
    font-size: 12px;
  }

  :deep(.el-table .el-textarea__inner) {
    font-size: 12px;
  }

  .array-manager {
    padding: 16px 0;
  }

  .array-header {
    margin-bottom: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .array-header span {
    font-weight: 500;
    color: #303133;
  }

  .object-manager {
    padding: 16px 0;
  }

  .object-header {
    margin-bottom: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .object-header span {
    font-weight: 500;
    color: #303133;
  }
</style>
