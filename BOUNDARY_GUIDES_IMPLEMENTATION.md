# 边界限制和参考线功能实现

## 🎯 功能概述

实现了两个核心功能：
1. **边界限制**：确保所有组件/容器在拖拽和调整大小时不能超出画布范围
2. **拖拽参考线**：拖拽时显示参考线，方便对齐其他组件/容器

## 🔧 实现细节

### 1. 边界限制功能

#### 拖拽边界限制 (handleDragMove & handleDragEnd)
```javascript
// 获取组件尺寸（显示坐标）
let componentWidth = component.size.width
let componentHeight = component.size.height

// 处理百分比转换
if (typeof componentWidth === 'string' && componentWidth.endsWith('%')) {
  componentWidth = (parseFloat(componentWidth) * rect.width) / 100
} else {
  componentWidth = (componentWidth * rect.width) / props.canvasSize.width
}

// 限制在画布边界内
newX = Math.max(0, Math.min(newX, rect.width - componentWidth))
newY = Math.max(0, Math.min(newY, rect.height - componentHeight))
```

#### 调整大小边界限制 (calculateNewDimensions)
```javascript
// 限制在画布边界内
if (x < 0) {
  width += x
  x = 0
}
if (y < 0) {
  height += y
  y = 0
}
if (x + width > rect.width) {
  width = rect.width - x
}
if (y + height > rect.height) {
  height = rect.height - y
}
```

### 2. 参考线功能

#### 参考线计算 (calculateGuidePositions)
```javascript
// 添加画布边界参考线
positions.vertical.add(0) // 左边界
positions.vertical.add(props.canvasSize.width) // 右边界
positions.vertical.add(props.canvasSize.width / 2) // 中心线

// 添加其他组件的参考线
positions.vertical.add(compX) // 左边界
positions.vertical.add(compX + compWidth) // 右边界
positions.vertical.add(compX + compWidth / 2) // 中心线
```

#### 参考线显示 (模板)
```vue
<!-- 垂直参考线 -->
<div
  v-for="(position, index) in guides.vertical"
  :key="'v-' + index"
  class="guide-line guide-line-vertical"
  :style="{ left: convertActualToDisplayX(position) + 'px' }"
></div>
```

#### 参考线吸附 (handleDragMove & handleDragEnd)
```javascript
guides.value.vertical.forEach(guidePos => {
  const displayGuidePos = (guidePos * rect.width) / props.canvasSize.width
  // 左边界吸附
  if (Math.abs(newX - displayGuidePos) < snapThreshold) {
    newX = displayGuidePos
  }
  // 右边界吸附
  if (Math.abs((newX + componentWidth) - displayGuidePos) < snapThreshold) {
    newX = displayGuidePos - componentWidth
  }
  // 中心线吸附
  if (Math.abs((newX + componentWidth/2) - displayGuidePos) < snapThreshold) {
    newX = displayGuidePos - componentWidth/2
  }
})
```

## 📋 修改的文件

### src/components/EditorCanvas.vue

#### 新增函数：
- `convertActualToDisplayX(actualX)` - 实际坐标转显示坐标X
- `convertActualToDisplayY(actualY)` - 实际坐标转显示坐标Y

#### 修改的函数：
- `handleDragMove()` - 添加边界限制和改进参考线吸附
- `handleDragEnd()` - 添加边界限制和改进参考线吸附
- `calculateNewDimensions()` - 添加调整大小时的边界限制
- `calculateGuidePositions()` - 改进参考线计算，支持所有组件类型

#### 模板修改：
- 参考线显示使用坐标转换函数

## ✅ 功能特性

### 边界限制
- ✅ 支持所有组件类型（不仅限于DIV容器）
- ✅ 拖拽时实时边界检测
- ✅ 调整大小时边界限制
- ✅ 支持百分比和像素单位
- ✅ 支持响应式和固定尺寸画布
- ✅ 考虑组件实际尺寸，确保完整组件在画布内

### 参考线
- ✅ 拖拽时自动显示参考线
- ✅ 包括画布边界参考线（左、右、上、下、中心）
- ✅ 包括其他组件参考线（边界和中心线）
- ✅ 5px吸附阈值，支持精确对齐
- ✅ 支持左对齐、右对齐、中心对齐
- ✅ 支持上对齐、下对齐、垂直中心对齐
- ✅ 拖拽结束时自动隐藏参考线
- ✅ 坐标转换确保在不同缩放下正确显示

## 🎨 视觉效果

### 参考线样式
```css
.guide-line-vertical {
  width: 1px;
  height: 100%;
  top: 0;
  background-color: #409eff;
}

.guide-line-horizontal {
  width: 100%;
  height: 1px;
  left: 0;
  background-color: #409eff;
}
```

## 🧪 测试

创建了测试页面 `test-boundary-guides.html` 用于验证功能：
- 边界限制演示
- 参考线显示演示
- 功能说明和实现细节

## 🔄 兼容性

- ✅ 保持与现有DIV容器碰撞检测的兼容性
- ✅ 支持响应式画布模式
- ✅ 支持固定尺寸画布模式
- ✅ 支持百分比和像素单位混合使用
- ✅ 不影响现有的拖拽和调整大小功能

## 📝 使用说明

1. **拖拽组件**：
   - 开始拖拽时自动显示参考线
   - 组件会自动吸附到参考线
   - 组件无法拖拽到画布外部

2. **调整大小**：
   - 调整大小时组件不会超出画布边界
   - 保持最小尺寸限制（50px）

3. **参考线对齐**：
   - 蓝色参考线表示可对齐位置
   - 包括画布边界和其他组件的边界
   - 5px范围内自动吸附

这个实现确保了用户在设计界面时有更好的体验，组件始终保持在画布范围内，并且可以精确对齐。
