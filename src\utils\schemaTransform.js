/**
 * 将平铺的组件数组转换为层级嵌套的schema结构
 * @param {Array} components - 平铺的组件数组
 * @returns {Array} - 层级嵌套的组件树
 */
export function transformToHierarchicalSchema(components) {
  const componentMap = new Map()
  const rootComponents = []

  // 创建组件映射表
  components.forEach(component => {
    componentMap.set(component.id, {
      ...component,
      children: []
    })
  })

  // 构建层级关系
  components.forEach(component => {
    const mappedComponent = componentMap.get(component.id)

    // 处理子容器关系
    if (component.config?.isChildContainer && component.config?.parentId) {
      const parentComponent = componentMap.get(component.config.parentId)
      if (parentComponent) {
        // 确保children数组存在
        if (!parentComponent.children) {
          parentComponent.children = []
        }
        // 按childIndex排序插入
        const childIndex = component.config.childIndex || 0
        parentComponent.children[childIndex] = mappedComponent
        return
      }
    }

    // 处理插槽子组件关系
    if (component.config?.isSlotChild && component.config?.parentContainerId) {
      const parentComponent = componentMap.get(
        component.config.parentContainerId
      )
      if (parentComponent) {
        // 确保children数组存在
        if (!parentComponent.children) {
          parentComponent.children = []
        }
        // 检查是否已经存在，避免重复添加
        const existingChild = parentComponent.children.find(
          child => child.id === mappedComponent.id
        )
        if (!existingChild) {
          parentComponent.children.push(mappedComponent)
        }
        return
      }
    }

    // 如果不是子组件，则为根组件
    if (!component.config?.isChildContainer && !component.config?.isSlotChild) {
      rootComponents.push(mappedComponent)
    }
  })

  // 处理插槽子组件的层级关系
  components.forEach(component => {
    if (component.config?.hasSlotChild && component.config?.slotChildId) {
      const parentComponent = componentMap.get(component.id)
      const slotChild = componentMap.get(component.config.slotChildId)

      if (parentComponent && slotChild) {
        // 确保children数组存在
        if (!parentComponent.children) {
          parentComponent.children = []
        }

        // 检查是否已经存在，避免重复添加
        const existingChild = parentComponent.children.find(
          child => child.id === slotChild.id
        )

        // 如果插槽子组件不在其他地方且不重复，添加到父组件的children中
        const isAlreadyChild = components.some(
          comp =>
            comp.config?.isChildContainer &&
            comp.config?.parentId === component.id
        )

        if (!isAlreadyChild && !existingChild) {
          parentComponent.children.push(slotChild)
        }
      }
    }
  })

  // 清理children数组，移除空元素并过滤掉null/undefined
  const cleanupChildren = component => {
    if (component.children) {
      component.children = component.children.filter(child => child != null)
      component.children.forEach(cleanupChildren)
    }
    return component
  }

  return rootComponents.map(cleanupChildren)
}

/**
 * 将层级schema转换回平铺结构（用于渲染兼容）
 * @param {Array} hierarchicalComponents - 层级组件树
 * @returns {Array} - 平铺的组件数组
 */
export function transformToFlatSchema(hierarchicalComponents) {
  const flatComponents = []

  const traverse = (component, parentId = null, childIndex = null) => {
    // 创建当前组件的副本
    const flatComponent = { ...component }

    // 如果有子组件，设置childrenIds和isContainer标志
    if (component.children && component.children.length > 0) {
      flatComponent.config = {
        ...flatComponent.config,
        isContainer: true,
        childrenIds: component.children.map(child => child.id)
      }
    }

    // 移除children属性，因为平铺结构不需要
    delete flatComponent.children

    // 添加父子关系配置
    if (parentId) {
      flatComponent.config = {
        ...flatComponent.config,
        isChildContainer: true,
        parentId,
        childIndex
      }
    }

    flatComponents.push(flatComponent)

    // 递归处理子组件
    if (component.children && component.children.length > 0) {
      component.children.forEach((child, index) => {
        traverse(child, component.id, index)
      })
    }
  }

  hierarchicalComponents.forEach(component => {
    traverse(component)
  })

  // 确保子容器关系正确性：验证所有childrenIds都指向有效的子容器
  const componentMap = new Map()
  flatComponents.forEach(comp => {
    componentMap.set(comp.id, comp)
  })

  // 修复可能的关系不一致
  flatComponents.forEach(comp => {
    if (comp.config?.isContainer && comp.config?.childrenIds) {
      // 验证子容器是否存在，并确保它们都标记为isChildContainer
      const validChildrenIds = comp.config.childrenIds.filter(childId => {
        const child = componentMap.get(childId)
        return (
          child &&
          child.config?.isChildContainer &&
          child.config?.parentId === comp.id
        )
      })

      // 更新childrenIds为有效的子容器ID
      comp.config.childrenIds = validChildrenIds

      // 如果没有有效的子容器，移除容器标记
      if (validChildrenIds.length === 0) {
        delete comp.config.isContainer
        delete comp.config.childrenIds
      }
    }
  })

  return flatComponents
}

/**
 * 生成层级schema格式
 * @param {Object} canvasSize - 画布尺寸
 * @param {Array} components - 组件数组
 * @returns {Object} - 完整的层级schema
 */
export function generateHierarchicalSchema(canvasSize, components) {
  return {
    canvas: {
      width: canvasSize.width,
      height: canvasSize.height
    },
    version: '2.0', // 标识为新版本的层级schema
    components: transformToHierarchicalSchema(components)
  }
}

/**
 * 清理组件配置，移除内部使用的配置项
 * @param {Object} component - 组件对象
 * @returns {Object} - 清理后的组件
 */
export function cleanComponentConfig(component) {
  const cleanComponent = {
    id: component.id,
    type: component.type,
    name: component.name || component.type,
    position: component.position,
    size: component.size,
    config: {},
    dataSource: component.dataSource
  }

  // 保留铺满画布相关的属性
  if (component.fullCanvas !== undefined) {
    cleanComponent.fullCanvas = component.fullCanvas
  }
  if (component.originalSize !== undefined) {
    cleanComponent.originalSize = component.originalSize
  }

  // 处理基础配置
  if (component.config) {
    const configToKeep = [
      'title',
      'showTitle',
      'showLegend',
      'showTooltip',
      'colors',
      'legendPosition',
      'legendOrient',
      'backgroundColor',
      'borderColor',
      'borderWidth',
      'borderStyle',
      'borderRadius',
      'padding',
      'content',
      'showTitleBar',
      'titleText',
      'titleHeight',
      'titleBackgroundColor',
      'titleTextColor',
      'titleFontSize',
      'titleAlign',
      // 表单相关配置
      'rows',
      'children',
      'labelWidth',
      'labelPosition',
      'size',
      'disabled',
      'inline',
      // 表单子组件配置
      'label',
      'placeholder',
      'value', // 保留表单组件的值
      'required',
      'clearable',
      'readonly',
      'type',
      'format',
      'valueFormat',
      'rangeSeparator',
      'startPlaceholder',
      'endPlaceholder',
      'options',
      'text',
      'loading',
      'plain',
      'round',
      'circle',
      'color',
      // 变量绑定相关配置
      'bindVariable',
      'variables',
      // DIV容器拆分相关配置
      'isContainer',
      'direction',
      'childrenIds',
      'parentPadding',
      'isChildContainer',
      'parentId',
      'childIndex',
      'parentDirection',
      'childGap'
    ]

    configToKeep.forEach(key => {
      if (component.config[key] !== undefined) {
        cleanComponent.config[key] = component.config[key]
      }
    })

    // 保留图表特有配置
    if (component.config.pieConfig) {
      cleanComponent.config.pieConfig = { ...component.config.pieConfig }
    }
    if (component.config.barConfig) {
      cleanComponent.config.barConfig = { ...component.config.barConfig }
    }
    if (component.config.lineConfig) {
      cleanComponent.config.lineConfig = { ...component.config.lineConfig }
    }

    // 保留自定义图表配置
    if (component.config.customConfig) {
      cleanComponent.config.customConfig = { ...component.config.customConfig }
    }

    // 保留ECharts选项配置
    if (component.config.options) {
      cleanComponent.config.options = { ...component.config.options }
    }

    // 深度保留表单行配置（包含表单列和子组件）
    if (component.config.rows && Array.isArray(component.config.rows)) {
      cleanComponent.config.rows = component.config.rows.map(row => ({
        ...row,
        children: row.children
          ? row.children.map(child => ({
              ...child,
              config: { ...child.config }
            }))
          : [],
        columns: row.columns
          ? row.columns.map(column => ({
              ...column,
              children: column.children
                ? column.children.map(child => ({
                    ...child,
                    config: { ...child.config }
                  }))
                : []
            }))
          : []
      }))
    }

    // 深度保留表单子组件配置（向后兼容）
    if (component.config.children && Array.isArray(component.config.children)) {
      cleanComponent.config.children = component.config.children.map(child => ({
        ...child,
        config: { ...child.config }
      }))
    }
  }

  // 递归清理子组件
  if (component.children && component.children.length > 0) {
    cleanComponent.children = component.children.map(cleanComponentConfig)
  }

  return cleanComponent
}
