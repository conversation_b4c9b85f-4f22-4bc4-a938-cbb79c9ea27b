<template>
  <div class="preview-page">
    <div v-if="schema" class="preview-container">
      <div class="preview-wrapper" :style="wrapperStyle">
        <SchemaRenderer
          :schema="processedSchema"
          is-preview
          @update-component="handleUpdateComponent"
        />
      </div>
    </div>
    <div v-else class="no-schema">
      <h1>没有可预览的内容</h1>
      <p>请返回编辑器页面，点击"预览"按钮生成预览。</p>
    </div>

    <!-- 调试信息 -->
    <div v-if="showDebugInfo" class="debug-info">
      <h3>调试信息</h3>
      <p>组件总数: {{ schema?.components?.length || 0 }}</p>
      <p>顶级组件: {{ topLevelComponentsCount }}</p>
      <p>子容器数: {{ childContainersCount }}</p>
      <div style="max-height: 200px; overflow-y: auto; font-size: 12px">
        <pre>{{ JSON.stringify(schema?.components, null, 2) }}</pre>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { ref, onMounted, computed, watch, onUnmounted } from 'vue'
  import { useWindowSize } from '@vueuse/core'
  import SchemaRenderer from '../components/SchemaRenderer.vue'

  const schema = ref(null)
  const showDebugInfo = ref(false) // 设置为 true 可查看调试信息
  const { width: windowWidth, height: windowHeight } = useWindowSize()

  // 创建style元素
  const createStyleElement = styles => {
    const styleId = 'schema-styles'
    let styleEl = document.getElementById(styleId)

    if (!styleEl) {
      styleEl = document.createElement('style')
      styleEl.id = styleId
      document.head.appendChild(styleEl)
    }

    styleEl.textContent = styles
  }

  // 移除style元素
  const removeStyleElement = () => {
    const styleEl = document.getElementById('schema-styles')
    if (styleEl) {
      styleEl.remove()
    }
  }

  // 监听schema变化，更新样式
  watch(
    () => schema.value?.styles,
    newStyles => {
      if (newStyles) {
        createStyleElement(newStyles)
      } else {
        removeStyleElement()
      }
    }
  )

  // 组件卸载时清理样式
  onUnmounted(() => {
    removeStyleElement()
  })

  // 调试计算属性
  const topLevelComponentsCount = computed(() => {
    if (!schema.value?.components) return 0
    return schema.value.components.filter(
      comp => !comp.config?.isChildContainer
    ).length
  })

  const childContainersCount = computed(() => {
    if (!schema.value?.components) return 0
    return schema.value.components.filter(comp => comp.config?.isChildContainer)
      .length
  })

  // 处理schema，现在组件都使用像素值，只需处理画布的百分比
  const processedSchema = computed(() => {
    if (!schema.value) return null

    const processedSchema = JSON.parse(JSON.stringify(schema.value))
    const canvas = processedSchema.canvas || {}

    // 处理百分比宽度（仅对画布）
    if (typeof canvas.width === 'string' && canvas.width.endsWith('%')) {
      const percentage = parseFloat(canvas.width) / 100
      processedSchema.canvas.width = Math.floor(windowWidth.value * percentage)
    }

    // 处理百分比高度（仅对画布）
    if (typeof canvas.height === 'string' && canvas.height.endsWith('%')) {
      const percentage = parseFloat(canvas.height) / 100
      processedSchema.canvas.height = Math.floor(
        windowHeight.value * percentage
      )
    }

    return processedSchema
  })

  const wrapperStyle = computed(() => {
    if (!processedSchema.value?.canvas) {
      return {}
    }

    const canvasWidth = processedSchema.value.canvas.width
    const canvasHeight = processedSchema.value.canvas.height

    // 按实际尺寸显示，确保与编辑器完全一致
    return {
      width: `${canvasWidth}px`,
      height: `${canvasHeight}px`,
      maxWidth: '100vw',
      maxHeight: '100vh'
    }
  })

  // 处理组件更新事件
  const handleUpdateComponent = updatedComponent => {
    if (!schema.value || !schema.value.components) return

    // 查找并更新对应的组件
    const componentIndex = schema.value.components.findIndex(
      comp => comp.id === updatedComponent.id
    )

    if (componentIndex !== -1) {
      // 更新组件数据
      schema.value.components[componentIndex] = {
        ...schema.value.components[componentIndex],
        ...updatedComponent
      }
    } else {
      // 如果没有找到直接匹配的组件，可能是表单容器中的子组件
      // 遍历表单容器查找子组件
      schema.value.components.forEach((component, index) => {
        if (component.type === 'form-container' && component.config) {
          let updated = false

          // 检查表单行中的子组件
          if (component.config.rows) {
            component.config.rows.forEach(row => {
              if (row.children) {
                const childIndex = row.children.findIndex(
                  child => child.id === updatedComponent.id
                )
                if (childIndex !== -1) {
                  row.children[childIndex] = {
                    ...row.children[childIndex],
                    ...updatedComponent
                  }
                  updated = true
                }
              }

              if (row.columns) {
                row.columns.forEach(column => {
                  if (column.children) {
                    const childIndex = column.children.findIndex(
                      child => child.id === updatedComponent.id
                    )
                    if (childIndex !== -1) {
                      column.children[childIndex] = {
                        ...column.children[childIndex],
                        ...updatedComponent
                      }
                      updated = true
                    }
                  }
                })
              }
            })
          }

          // 检查直接子组件（向后兼容）
          if (!updated && component.config.children) {
            const childIndex = component.config.children.findIndex(
              child => child.id === updatedComponent.id
            )
            if (childIndex !== -1) {
              component.config.children[childIndex] = {
                ...component.config.children[childIndex],
                ...updatedComponent
              }
              updated = true
            }
          }

          if (updated) {
            // 触发响应式更新
            schema.value.components[index] = { ...component }
          }
        }
      })
    }
  }

  onMounted(async () => {
    const savedSchema = localStorage.getItem('bi-schema-preview')
    if (savedSchema) {
      try {
        const parsedSchema = JSON.parse(savedSchema)

        // 如果是新版本的层级schema，转换为平铺格式用于渲染
        if (parsedSchema.version === '2.0') {
          // 动态导入转换工具
          const { transformToFlatSchema } = await import(
            '../utils/schemaTransform.js'
          )

          // 转换为平铺格式
          const flatComponents = transformToFlatSchema(parsedSchema.components)

          console.log('预览页面 - 原始层级数据:', parsedSchema.components)
          console.log('预览页面 - 转换后平铺数据:', flatComponents)

          schema.value = {
            canvas: parsedSchema.canvas,
            components: flatComponents,
            methods: parsedSchema.methods || '',
            variables: parsedSchema.variables || {},
            mounted: parsedSchema.mounted || '',
            styles: parsedSchema.styles || ''
          }

          // 打印样式代码以便调试
          if (parsedSchema.styles) {
            console.log('预览页面 - 样式代码:', parsedSchema.styles)
          }
        }
      } catch (error) {
        console.error('解析schema数据失败:', error)
      }
    }
  })
</script>

<style scoped>
  .preview-page {
    width: 100%;
    height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #f5f5f5;
    overflow: auto;
  }

  .preview-container {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
  }

  .preview-wrapper {
    background-color: #fff;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
  }

  .no-schema {
    text-align: center;
    color: #909399;
  }

  .no-schema h1 {
    font-size: 24px;
    margin-bottom: 16px;
  }

  .debug-info {
    position: fixed;
    top: 20px;
    right: 20px;
    background: rgba(255, 255, 255, 0.9);
    padding: 16px;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    max-width: 400px;
    z-index: 1000;
  }

  .debug-info h3 {
    margin: 0 0 12px 0;
    font-size: 16px;
  }

  .debug-info p {
    margin: 8px 0;
    font-size: 14px;
  }
</style>
