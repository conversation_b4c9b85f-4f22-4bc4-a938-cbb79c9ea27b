<script>
  // 为递归组件定义名称
  export default {
    name: 'RenderComponent'
  }
</script>

<template>
  <div
    class="render-component"
    :class="{ 'full-canvas-mode': component.fullCanvas }"
  >
    <!-- 组件为空时的占位符 -->
    <div v-if="!component" class="empty-component">
      <div class="empty-content">
        <el-icon size="32"><Warning /></el-icon>
        <p>组件数据为空</p>
      </div>
    </div>

    <!-- 静态图表组件 -->
    <div
      v-else-if="isStaticChart"
      class="chart-container"
      :style="chartContainerStyle"
    >
      <div :id="chartId" class="chart-content" ref="chartRef"></div>
    </div>

    <!-- API数据表格 -->
    <div v-else-if="component.type === 'api-table'" class="api-table-container">
      <el-table
        :data="apiData || []"
        class="api-table"
        size="small"
        height="100%"
      >
        <el-table-column
          v-for="column in component.config.columns"
          :key="column.key"
          :prop="column.key"
          :label="column.label"
          :width="column.width"
        />
      </el-table>
      <div v-if="loading" class="loading-overlay">
        <div class="loading-spinner"></div>
      </div>
    </div>

    <!-- API指标卡 -->
    <div
      v-else-if="component.type === 'api-indicator'"
      class="api-indicator-container"
    >
      <div class="indicator-content">
        <div class="indicator-value">
          {{ formatIndicatorValue(apiData) }}
        </div>
      </div>
      <div v-if="loading" class="loading-overlay">
        <div class="loading-spinner"></div>
      </div>
    </div>

    <!-- DIV容器 -->
    <div
      v-else-if="component.type === 'div-container'"
      class="div-container"
      :style="divContainerStyle"
      @dragover="handleDivContainerDragOver"
      @drop="handleDivContainerDrop"
    >
      <!-- 如果是容器组，渲染子容器 -->
      <template v-if="component.config?.isContainer">
        <!-- 预览模式下渲染子容器 -->
        <template v-if="isPreview">
          <!-- 有标题栏的容器组 -->
          <template v-if="component.config?.showTitleBar">
            <div class="div-container-with-title">
              <!-- 标题栏 -->
              <div class="div-title-bar" :style="titleBarStyle">
                {{ component.config?.titleText || '标题' }}
              </div>
              <!-- 内容区域 - 渲染子容器 -->
              <div class="div-content-area" :style="childContainerAreaStyle">
                <div
                  v-for="childContainer in childContainers"
                  :key="childContainer.id"
                  class="child-container"
                  :style="childContainerStyle"
                >
                  <RenderComponent
                    :component="childContainer"
                    :is-preview="isPreview"
                    :all-components="allComponents"
                    :schema="schema"
                  />
                </div>
              </div>
            </div>
          </template>
          <!-- 无标题栏的容器组 -->
          <template v-else>
            <div class="div-container-content" :style="childContainerAreaStyle">
              <div
                v-for="childContainer in childContainers"
                :key="childContainer.id"
                class="child-container"
                :style="childContainerStyle"
              >
                <RenderComponent
                  :component="childContainer"
                  :is-preview="isPreview"
                  :all-components="allComponents"
                  :schema="schema"
                />
              </div>
            </div>
          </template>
        </template>

        <!-- 编辑器模式下显示占位符 -->
        <template v-else>
          <!-- 有标题栏的容器组 -->
          <template v-if="component.config?.showTitleBar">
            <div class="div-container-with-title">
              <!-- 标题栏 -->
              <div class="div-title-bar" :style="titleBarStyle">
                {{ component.config?.titleText || '标题' }}
              </div>
              <!-- 内容区域 -->
              <div
                class="div-content-area"
                :style="childContainerAreaStyle"
              ></div>
            </div>
          </template>
          <!-- 无标题栏的容器组 -->
          <template v-else>
            <div
              class="div-container-content"
              :style="childContainerAreaStyle"
            ></div>
          </template>
        </template>
      </template>

      <!-- 普通单一容器或子容器（叶子节点） -->
      <template v-else>
        <!-- 有标题栏的容器 -->
        <template v-if="component.config?.showTitleBar">
          <div class="div-container-with-title">
            <!-- 标题栏 -->
            <div class="div-title-bar" :style="titleBarStyle">
              {{ component.config?.titleText || '标题' }}
            </div>
            <!-- 内容区域 -->
            <div class="div-content-area" :style="contentAreaStyle">
              <!-- 如果容器有插槽子组件，渲染子组件 -->
              <template
                v-if="component.config?.hasSlotChild && slotChildComponent"
              >
                <div
                  class="slot-child-wrapper"
                  :class="{ 'slot-child-selected': isSlotChildSelected }"
                  @click.stop="handleSlotChildClick(slotChildComponent)"
                >
                  <RenderComponent
                    :component="slotChildComponent"
                    :is-preview="isPreview"
                    :all-components="allComponents"
                    :selected-component="selectedComponent"
                    :schema="schema"
                    class="slot-child-component"
                  />
                  <!-- 删除按钮 -->
                  <div
                    v-if="isSlotChildSelected && !isPreview"
                    class="slot-child-delete-btn"
                    @click.stop="handleSlotChildDelete"
                  >
                    <ElIcon><Delete /></ElIcon>
                  </div>
                </div>
              </template>
              <!-- 否则显示容器内容 -->
              <template
                v-else-if="
                  component.config?.content && component.config.content.trim()
                "
              >
                {{ component.config.content }}
              </template>
              <!-- 空容器的占位符 -->
              <template v-else>
                <div class="empty-container-placeholder">
                  <span v-if="!isPreview">空容器</span>
                </div>
              </template>
            </div>
          </div>
        </template>

        <!-- 无标题栏的容器 -->
        <template v-else>
          <div class="div-container-content" :style="contentAreaStyle">
            <!-- 如果容器有插槽子组件，渲染子组件 -->
            <template
              v-if="component.config?.hasSlotChild && slotChildComponent"
            >
              <div
                class="slot-child-wrapper"
                :class="{ 'slot-child-selected': isSlotChildSelected }"
                @click.stop="handleSlotChildClick(slotChildComponent)"
              >
                <RenderComponent
                  :component="slotChildComponent"
                  :is-preview="isPreview"
                  :all-components="allComponents"
                  :selected-component="selectedComponent"
                  :schema="schema"
                  class="slot-child-component"
                />
                <!-- 删除按钮 -->
                <div
                  v-if="isSlotChildSelected && !isPreview"
                  class="slot-child-delete-btn"
                  @click.stop="handleSlotChildDelete"
                >
                  <ElIcon><Delete /></ElIcon>
                </div>
              </div>
            </template>
            <!-- 否则显示容器内容 -->
            <template
              v-else-if="
                component.config?.content && component.config.content.trim()
              "
            >
              {{ component.config.content }}
            </template>
            <!-- 空容器的占位符 -->
            <template v-else>
              <div class="empty-container-placeholder">
                <span v-if="!isPreview">空容器</span>
              </div>
            </template>
          </div>
        </template>
      </template>
    </div>

    <!-- API图表组件 -->
    <div
      v-else-if="component.type === 'api-chart'"
      class="api-chart-container"
      :style="chartContainerStyle"
    >
      <div :id="'api-' + chartId" class="chart-content" ref="apiChartRef"></div>
      <div v-if="loading" class="loading-overlay">
        <div class="loading-spinner"></div>
      </div>
    </div>

    <!-- 表单组件 -->
    <!-- 输入框 -->
    <div
      v-else-if="component.type === 'input'"
      class="form-component input-component"
    >
      <ElFormItem
        :label="component.config?.label || '输入框'"
        :label-width="component.config?.labelWidth || '80px'"
        :label-position="component.config?.labelPosition || 'left'"
        :required="component.config?.required || false"
        class="form-item"
      >
        <ElInput
          :model-value="getFormChildValue(component)"
          :placeholder="component.config?.placeholder || '请输入内容'"
          :disabled="component.config?.disabled || false"
          :clearable="component.config?.clearable !== false"
          :show-password="component.config?.showPassword || false"
          :maxlength="component.config?.maxlength"
          :show-word-limit="component.config?.showWordLimit || false"
          :size="component.config?.size || 'default'"
          @input="handleFormChildInput(component, $event)"
          @change="handleFormChildChange(component, $event)"
        />
      </ElFormItem>
    </div>

    <!-- 下拉选择 -->
    <div
      v-else-if="component.type === 'select'"
      class="form-component select-component"
    >
      <ElFormItem
        :label="component.config?.label || '下拉选择'"
        :label-width="component.config?.labelWidth || '80px'"
        :label-position="component.config?.labelPosition || 'left'"
        class="form-item"
      >
        <ElSelect
          :model-value="getFormChildValue(component)"
          :placeholder="component.config?.placeholder || '请选择'"
          :disabled="component.config?.disabled || false"
          :clearable="component.config?.clearable !== false"
          :filterable="component.config?.filterable || false"
          :multiple="component.config?.multiple || false"
          :size="component.config?.size || 'default'"
          @change="handleFormChildChange(component, $event)"
        >
          <ElOption
            v-for="option in component.config?.options || []"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </ElSelect>
      </ElFormItem>
    </div>

    <!-- 时间控件 -->
    <div
      v-else-if="component.type === 'datepicker'"
      class="form-component datepicker-component"
    >
      <ElFormItem
        :label="component.config?.label || '时间控件'"
        :label-width="component.config?.labelWidth || '80px'"
        :label-position="component.config?.labelPosition || 'left'"
        class="form-item"
      >
        <ElDatePicker
          :model-value="getFormChildValue(component)"
          :type="component.config?.type || 'date'"
          :placeholder="component.config?.placeholder || '请选择日期'"
          :format="component.config?.format || 'YYYY-MM-DD'"
          :value-format="component.config?.valueFormat || 'YYYY-MM-DD'"
          :disabled="component.config?.disabled || false"
          :clearable="component.config?.clearable !== false"
          :size="component.config?.size || 'default'"
          :range-separator="component.config?.rangeSeparator || '至'"
          :start-placeholder="component.config?.startPlaceholder || '开始日期'"
          :end-placeholder="component.config?.endPlaceholder || '结束日期'"
          @update:model-value="handleFormChildChange(component, $event)"
        />
      </ElFormItem>
    </div>

    <!-- 按钮 -->
    <div
      v-else-if="component.type === 'button'"
      class="form-component button-component"
    >
      <ElButton
        :type="component.config?.type || 'primary'"
        :size="component.config?.size || 'default'"
        :disabled="component.config?.disabled || false"
        :loading="component.config?.loading || false"
        :plain="component.config?.plain || false"
        :round="component.config?.round || false"
        :circle="component.config?.circle || false"
        :color="component.config?.color || ''"
        @click="handleButtonClick"
      >
        <ElIcon v-if="component.config?.icon" :name="component.config.icon" />
        {{ component.config?.text || '按钮' }}
      </ElButton>
    </div>

    <!-- 表单容器 -->
    <div
      v-else-if="component.type === 'form-container'"
      class="form-container"
      :class="{ 'form-container-preview': isPreview }"
      :style="formContainerStyle"
      @dragover="handleFormContainerDragOver"
      @drop="handleFormContainerDrop"
    >
      <!-- 容器标题 -->
      <div
        v-if="component.config?.showTitle !== false"
        class="form-container-title"
        :style="formContainerTitleStyle"
      >
        {{ component.config?.titleText || '表单标题' }}
      </div>

      <!-- 表单内容区域 -->
      <div
        class="form-container-content"
        :class="{ 'form-container-preview': isPreview }"
        :style="formContainerContentStyle"
      >
        <ElForm
          :model="formData"
          :label-position="component.config?.labelPosition || 'top'"
          :label-width="component.config?.labelWidth || 'auto'"
          :size="component.config?.size || 'default'"
          class="form-container-form"
          :class="{ 'form-container-form-preview': isPreview }"
          @click="handleFormContainerClick"
        >
          <!-- 表单行渲染 -->
          <template
            v-if="component.config?.rows && component.config.rows.length > 0"
          >
            <div
              v-for="(row, rowIndex) in component.config.rows"
              :key="row.id || rowIndex"
              class="form-row"
              :class="{
                'form-row-selected': selectedFormRow?.id === row.id,
                'form-row-preview': isPreview,
                'form-row-hover': !isPreview
              }"
              :style="getFormRowStyle(row)"
              :title="!isPreview ? `第${rowIndex + 1}行 - 点击选中` : ''"
              @click="handleRowClick(row)"
              @contextmenu.stop="
                !isPreview && handleFormRowContextMenu(row, $event)
              "
            >
              <!-- 行选中区域指示器 (非预览模式下显示) -->
              <div
                v-if="!isPreview"
                class="form-row-selector"
                @click.stop="selectFormRow(row)"
                :title="`点击选中第${rowIndex + 1}行`"
              >
                <span class="row-number">{{ rowIndex + 1 }}</span>
              </div>

              <!-- 行操作按钮 (非预览模式下显示) -->
              <div v-if="!isPreview" class="form-row-actions">
                <ElPopover placement="bottom" :width="160" trigger="click">
                  <template #reference>
                    <ElButton
                      type="link"
                      size="small"
                      :icon="MoreFilled"
                      class="action-btn"
                      @click.stop
                    />
                  </template>
                  <div class="action-menu">
                    <ElButton
                      type="link"
                      size="small"
                      :icon="Edit"
                      @click="editFormRow(row)"
                      class="menu-item"
                    >
                      编辑行
                    </ElButton>
                    <ElButton
                      type="link"
                      size="small"
                      :icon="ArrowUp"
                      @click="moveFormRow(rowIndex, 'up')"
                      :disabled="rowIndex === 0"
                      class="menu-item"
                    >
                      上移
                    </ElButton>
                    <ElButton
                      type="link"
                      size="small"
                      :icon="ArrowDown"
                      @click="moveFormRow(rowIndex, 'down')"
                      :disabled="rowIndex === component.config.rows.length - 1"
                      class="menu-item"
                    >
                      下移
                    </ElButton>
                    <ElButton
                      type="link"
                      size="small"
                      :icon="Delete"
                      @click="deleteFormRow(rowIndex)"
                      class="menu-item delete-btn"
                    >
                      删除行
                    </ElButton>
                  </div>
                </ElPopover>
              </div>

              <!-- 行内容 -->
              <div class="form-row-content" @click.stop>
                <!-- 如果有列结构，渲染列 -->
                <template v-if="row.columns && row.columns.length > 0">
                  <ElRow class="form-row-layout" :gutter="8">
                    <ElCol
                      v-for="(column, colIndex) in row.columns"
                      :key="column.id || colIndex"
                      :span="getColumnSpan(row, colIndex)"
                      class="form-column"
                      :class="{
                        'form-column-drag-over':
                          dragOverColumn?.id === column.id,
                        'form-column-selected':
                          selectedFormColumn?.column?.id === column.id,
                        'form-column-preview': isPreview,
                        'form-column-adjust-mode':
                          adjustPositionMode && !isPreview
                      }"
                    >
                      <div
                        class="form-column-inner"
                        :style="{
                          borderWidth: column.borderWidth
                            ? `${column.borderWidth}px`
                            : undefined,
                          borderStyle: column.borderStyle || undefined,
                          borderColor: column.borderColor || undefined,
                          padding: column.padding
                            ? `${column.padding}px`
                            : undefined,
                          backgroundColor: column.backgroundColor || undefined,
                          cursor: !isPreview ? 'pointer' : 'default',
                          minHeight: '60px'
                        }"
                        @dragover="handleColumnDragOver(column, row, $event)"
                        @dragleave="handleColumnDragLeave"
                        @drop="handleColumnDrop(column, row, $event)"
                        @click.stop="
                          !isPreview &&
                          (adjustPositionMode
                            ? handleColumnClickForAdjust(column, row)
                            : selectFormColumn(column, row))
                        "
                        @contextmenu.stop="
                          !isPreview &&
                          handleFormColumnContextMenu(column, row, $event)
                        "
                      >
                        <!-- 列内的表单元素 -->
                        <template
                          v-if="column.children && column.children.length > 0"
                        >
                          <!-- 分离按钮和其他表单元素 -->
                          <template
                            v-for="(
                              child, childIndex
                            ) in separateButtonsAndOthers(column.children)
                              .others"
                            :key="'other_' + (child.id || childIndex)"
                          >
                            <div
                              class="form-column-child form-child-item"
                              :class="{
                                'form-child-selected':
                                  selectedFormChild?.id === child.id,
                                'form-child-preview': isPreview,
                                'form-child-dragging':
                                  draggedFormChild?.id === child.id,
                                'form-child-adjusting':
                                  componentToAdjust?.id === child.id
                              }"
                              :draggable="!isPreview"
                              @click.stop="!isPreview && selectFormChild(child)"
                              @mousedown.stop="!isPreview"
                              @dragstart="
                                !isPreview &&
                                handleFormChildDragStart(
                                  child,
                                  column,
                                  row,
                                  $event
                                )
                              "
                              @dragend="
                                !isPreview && handleFormChildDragEnd($event)
                              "
                            >
                              <!-- 操作按钮 (非预览模式下显示) -->
                              <div v-if="!isPreview" class="form-child-actions">
                                <ElPopover
                                  placement="bottom"
                                  :width="160"
                                  trigger="click"
                                >
                                  <template #reference>
                                    <ElButton
                                      type="link"
                                      size="small"
                                      :icon="MoreFilled"
                                      class="action-btn"
                                      @click.stop
                                    />
                                  </template>
                                  <div class="action-menu">
                                    <ElButton
                                      type="link"
                                      size="small"
                                      :icon="Position"
                                      @click="
                                        startAdjustPosition(child, column, row)
                                      "
                                      class="menu-item"
                                    >
                                      调整位置
                                    </ElButton>
                                    <ElButton
                                      type="link"
                                      size="small"
                                      :icon="Delete"
                                      @click="
                                        deleteColumnFormChild(
                                          column,
                                          row,
                                          column.children.indexOf(child)
                                        )
                                      "
                                      class="menu-item delete-btn"
                                    >
                                      删除
                                    </ElButton>
                                  </div>
                                </ElPopover>
                              </div>
                              <!-- 非按钮表单元素渲染 -->
                              <component
                                :is="getFormItemComponent(child.type)"
                                :label="child.config?.label || '未命名'"
                                :label-width="
                                  child.config?.labelWidth ||
                                  component.config?.labelWidth
                                "
                                :required="child.config?.required"
                                :size="
                                  child.config?.size ||
                                  component.config?.size ||
                                  'default'
                                "
                                class="form-child-form-item"
                              >
                                <component
                                  :is="getFormControlComponent(child.type)"
                                  :key="
                                    child.type === 'datepicker'
                                      ? `${child.id}-${getFormChildValue(child)}`
                                      : child.id
                                  "
                                  v-bind="getFormControlProps(child)"
                                  @input="
                                    child.type !== 'datepicker'
                                      ? handleFormChildInput(child, $event)
                                      : undefined
                                  "
                                  @change="
                                    child.type !== 'datepicker'
                                      ? handleFormChildChange(child, $event)
                                      : undefined
                                  "
                                  @click="handleFormChildClick(child)"
                                >
                                  <template v-if="child.type === 'select'">
                                    <ElOption
                                      v-for="option in child.config?.options ||
                                      []"
                                      :key="option.value"
                                      :label="option.label"
                                      :value="option.value"
                                    />
                                  </template>
                                </component>
                              </component>
                            </div>
                          </template>

                          <!-- 按钮组容器 -->
                          <div
                            v-if="
                              separateButtonsAndOthers(column.children).buttons
                                .length > 0
                            "
                            class="form-column-button-group"
                          >
                            <div
                              v-for="(
                                button, buttonIndex
                              ) in separateButtonsAndOthers(column.children)
                                .buttons"
                              :key="'button_' + (button.id || buttonIndex)"
                              class="form-column-button-item form-child-item"
                              :class="{
                                'form-child-selected':
                                  selectedFormChild?.id === button.id,
                                'form-child-preview': isPreview,
                                'form-child-dragging':
                                  draggedFormChild?.id === button.id,
                                'form-child-adjusting':
                                  componentToAdjust?.id === button.id
                              }"
                              :draggable="!isPreview"
                              @click.stop="
                                !isPreview && selectFormChild(button)
                              "
                              @mousedown.stop="!isPreview"
                              @dragstart="
                                !isPreview &&
                                handleFormChildDragStart(
                                  button,
                                  column,
                                  row,
                                  $event
                                )
                              "
                              @dragend="
                                !isPreview && handleFormChildDragEnd($event)
                              "
                            >
                              <!-- 操作按钮 (非预览模式下显示) -->
                              <div v-if="!isPreview" class="form-child-actions">
                                <ElPopover
                                  placement="bottom"
                                  :width="160"
                                  trigger="click"
                                >
                                  <template #reference>
                                    <ElButton
                                      type="link"
                                      size="small"
                                      :icon="MoreFilled"
                                      class="action-btn"
                                      @click.stop
                                    />
                                  </template>
                                  <div class="action-menu">
                                    <ElButton
                                      type="link"
                                      size="small"
                                      :icon="Position"
                                      @click="
                                        startAdjustPosition(button, column, row)
                                      "
                                      class="menu-item"
                                    >
                                      调整位置
                                    </ElButton>
                                    <ElButton
                                      type="link"
                                      size="small"
                                      :icon="Delete"
                                      @click="
                                        deleteColumnFormChild(
                                          column,
                                          row,
                                          column.children.indexOf(button)
                                        )
                                      "
                                      class="menu-item delete-btn"
                                    >
                                      删除
                                    </ElButton>
                                  </div>
                                </ElPopover>
                              </div>
                              <!-- 按钮渲染 -->
                              <div class="form-child-form-item">
                                <component
                                  :is="getFormControlComponent(button.type)"
                                  :key="
                                    button.type === 'datepicker'
                                      ? `${button.id}-${getFormChildValue(button)}`
                                      : button.id
                                  "
                                  v-bind="getFormControlProps(button)"
                                  @input="
                                    button.type !== 'datepicker'
                                      ? handleFormChildInput(button, $event)
                                      : undefined
                                  "
                                  @change="
                                    button.type !== 'datepicker'
                                      ? handleFormChildChange(button, $event)
                                      : undefined
                                  "
                                  @click="handleFormChildClick(button)"
                                >
                                  {{ button.config?.text || '按钮' }}
                                </component>
                              </div>
                            </div>
                          </div>
                        </template>
                        <div
                          v-else
                          class="form-column-empty"
                          @click.stop="
                            !isPreview &&
                            (adjustPositionMode
                              ? handleColumnClickForAdjust(column, row)
                              : selectFormColumn(column, row))
                          "
                        >
                          拖拽表单元素到此列
                        </div>
                      </div>
                    </ElCol>
                  </ElRow>
                </template>

                <!-- 没有列结构时显示原有的行内表单元素 -->
                <template v-else-if="row.children && row.children.length > 0">
                  <div
                    v-for="(child, childIndex) in row.children"
                    :key="child.id || childIndex"
                    class="form-row-child"
                  >
                    <!-- 按钮组件不需要显示标签 -->
                    <div
                      v-if="child.type === 'button'"
                      class="form-child-form-item"
                    >
                      <component
                        :is="getFormControlComponent(child.type)"
                        :key="
                          child.type === 'datepicker'
                            ? `${child.id}-${getFormChildValue(child)}`
                            : child.id
                        "
                        v-bind="getFormControlProps(child)"
                        @input="handleFormChildInput(child, $event)"
                        @change="handleFormChildChange(child, $event)"
                        @click="handleFormChildClick(child)"
                      >
                        {{ child.config?.text || '按钮' }}
                      </component>
                    </div>

                    <!-- 其他表单组件显示标签 -->
                    <component
                      v-else
                      :is="getFormItemComponent(child.type)"
                      :label="child.config?.label || '未命名'"
                      :label-width="
                        child.config?.labelWidth || component.config?.labelWidth
                      "
                      :required="child.config?.required"
                      :size="
                        child.config?.size ||
                        component.config?.size ||
                        'default'
                      "
                      class="form-child-form-item"
                    >
                      <component
                        :is="getFormControlComponent(child.type)"
                        :key="
                          child.type === 'datepicker'
                            ? `${child.id}-${getFormChildValue(child)}`
                            : child.id
                        "
                        v-bind="getFormControlProps(child)"
                        @input="handleFormChildInput(child, $event)"
                        @change="handleFormChildChange(child, $event)"
                        @click="handleFormChildClick(child)"
                      >
                        <!-- 为下拉选择添加选项 -->
                        <template v-if="child.type === 'select'">
                          <ElOption
                            v-for="option in child.config?.options || []"
                            :key="option.value"
                            :label="option.label"
                            :value="option.value"
                          />
                        </template>
                      </component>
                    </component>
                  </div>
                </template>
                <div
                  v-else
                  class="form-row-empty"
                  @click="!isPreview && selectFormRow(row)"
                >
                  拖拽表单元素到此行，或右键拆分列
                </div>
              </div>

              <!-- 行高度调整手柄 -->
              <div
                v-if="!isPreview && selectedFormRow?.id === row.id"
                class="row-resize-handle"
                @mousedown.stop="startRowResize(row, $event)"
              ></div>
            </div>
          </template>

          <!-- 原有的children渲染 (保持向后兼容) -->
          <template
            v-if="
              component.config?.children && component.config.children.length > 0
            "
          >
            <div
              v-for="(child, index) in component.config.children"
              :key="child.id || index"
              class="form-child-item"
              :class="{
                'form-child-selected': selectedFormChild?.id === child.id,
                'form-child-preview': isPreview
              }"
              :style="getFormChildStyle()"
              @click.stop="!isPreview && selectFormChild(child)"
            >
              <!-- 操作按钮 (非预览模式下显示) -->
              <div v-if="!isPreview" class="form-child-actions">
                <ElPopover placement="bottom" :width="160" trigger="click">
                  <template #reference>
                    <ElButton
                      type="link"
                      size="small"
                      :icon="MoreFilled"
                      class="action-btn"
                      @click.stop
                    />
                  </template>
                  <div class="action-menu">
                    <ElButton
                      type="link"
                      size="small"
                      :icon="Edit"
                      @click="editFormChild(child)"
                      class="menu-item"
                    >
                      编辑
                    </ElButton>
                    <ElButton
                      type="link"
                      size="small"
                      :icon="ArrowUp"
                      @click="moveFormChild(index, 'up')"
                      :disabled="index === 0"
                      class="menu-item"
                    >
                      上移
                    </ElButton>
                    <ElButton
                      type="link"
                      size="small"
                      :icon="ArrowDown"
                      @click="moveFormChild(index, 'down')"
                      :disabled="index === component.config.children.length - 1"
                      class="menu-item"
                    >
                      下移
                    </ElButton>
                    <ElButton
                      type="link"
                      size="small"
                      :icon="Delete"
                      @click="deleteFormChild(index)"
                      class="menu-item delete-btn"
                    >
                      删除
                    </ElButton>
                  </div>
                </ElPopover>
              </div>

              <!-- 按钮组件不需要显示标签 -->
              <div v-if="child.type === 'button'" class="form-child-form-item">
                <component
                  :is="getFormControlComponent(child.type)"
                  :key="
                    child.type === 'datepicker'
                      ? `${child.id}-${getFormChildValue(child)}`
                      : child.id
                  "
                  v-bind="getFormControlProps(child)"
                  @input="handleFormChildInput(child, $event)"
                  @change="handleFormChildChange(child, $event)"
                  @click="handleFormChildClick(child)"
                >
                  {{ child.config?.text || '按钮' }}
                </component>
              </div>

              <!-- 其他表单组件显示标签 -->
              <component
                v-else
                :is="getFormItemComponent(child.type)"
                :label="child.config?.label || '未命名'"
                :label-width="
                  child.config?.labelWidth || component.config?.labelWidth
                "
                :required="child.config?.required"
                :size="
                  child.config?.size || component.config?.size || 'default'
                "
                class="form-child-form-item"
              >
                <component
                  :is="getFormControlComponent(child.type)"
                  :key="
                    child.type === 'datepicker'
                      ? `${child.id}-${getFormChildValue(child)}`
                      : child.id
                  "
                  v-bind="getFormControlProps(child)"
                  @input="handleFormChildInput(child, $event)"
                  @change="handleFormChildChange(child, $event)"
                  @click="handleFormChildClick(child)"
                >
                  <!-- 为下拉选择添加选项 -->
                  <template v-if="child.type === 'select'">
                    <ElOption
                      v-for="option in child.config?.options || []"
                      :key="option.value"
                      :label="option.label"
                      :value="option.value"
                    />
                  </template>
                </component>
              </component>
            </div>
          </template>

          <!-- 完全空状态：没有行也没有子组件 -->
          <div
            v-if="
              (!component.config?.rows || component.config.rows.length === 0) &&
              (!component.config?.children ||
                component.config.children.length === 0)
            "
            class="form-container-empty"
          >
            拖拽表单元素到此处
          </div>

          <!-- 弱化提示：有内容时显示在底部 -->
          <div v-else-if="!isPreview" class="form-container-hint">
            右键新增行或拖拽更多表单元素
          </div>
        </ElForm>
      </div>
    </div>

    <!-- 未知组件类型 -->
    <div v-else class="unknown-component">
      <div class="unknown-content">
        <el-icon size="32"><Warning /></el-icon>
        <p>未知组件类型: {{ component.type }}</p>
      </div>
    </div>
  </div>
</template>

<script setup>
  import {
    ref,
    computed,
    defineProps,
    defineEmits,
    onMounted,
    onUnmounted,
    watch,
    nextTick
  } from 'vue'
  import {
    ElTable,
    ElTableColumn,
    ElIcon,
    ElForm,
    ElFormItem,
    ElInput,
    ElSelect,
    ElOption,
    ElDatePicker,
    ElButton,
    ElMessage,
    ElMessageBox,
    ElPopover,
    ElRow,
    ElCol
  } from 'element-plus'
  import {
    Warning,
    Edit,
    Delete,
    ArrowUp,
    ArrowDown,
    MoreFilled,
    Position
  } from '@element-plus/icons-vue'
  import { useEcharts } from '../composables/useEcharts'
  import {
    applyColorConfig,
    updateBarChartOptions,
    updateLineChartOptions,
    updatePieChartOptions,
    getChartTemplate
  } from '../utils/chartTemplates.js'
  import * as echarts from 'echarts/core'
  import { cloneDeep } from 'lodash-es'

  // Vue 3 递归组件：使用文件名作为组件名

  // 定义props
  const props = defineProps({
    component: {
      type: Object,
      required: true
    },
    isPreview: {
      type: Boolean,
      default: false
    },
    allComponents: {
      type: Array,
      default: () => []
    },
    selectedComponent: {
      type: Object,
      default: null
    },
    selectedFormChild: {
      type: Object,
      default: null
    },
    selectedFormRow: {
      type: Object,
      default: null
    },
    selectedFormColumn: {
      type: Object,
      default: null
    },
    schema: {
      type: Object,
      default: () => ({})
    }
  })

  // 定义事件
  const emit = defineEmits([
    'select-component',
    'delete-component',
    'select-form-child',
    'update-form-child',
    'delete-form-child',
    'reorder-form-children',
    'select-form-row',
    'update-form-row',
    'delete-form-row',
    'reorder-form-rows',
    'form-row-context-menu',
    'drop-to-column',
    'delete-column-form-child',
    'select-form-column',
    'form-column-context-menu',
    'start-adjust-position',
    'column-click-for-adjust'
  ])

  // ref引用
  const chartRef = ref(null)
  const apiChartRef = ref(null)

  // 响应式数据
  const apiData = ref(null)
  const loading = ref(false)
  const formData = ref({})

  // 表单变量数据管理
  const formVariables = ref({})

  // 初始化表单容器的变量数据
  const initFormVariables = () => {
    if (component.value.type === 'form-container') {
      const variables = component.value.config?.variables || []
      const variableData = {}

      variables.forEach(variable => {
        if (!formVariables.value[variable.id]) {
          // 根据变量类型设置默认值
          let defaultValue = variable.defaultValue
          if (
            defaultValue !== undefined &&
            defaultValue !== null &&
            defaultValue !== ''
          ) {
            try {
              // 尝试解析默认值
              if (variable.type === 'number') {
                defaultValue = Number(defaultValue) || 0
              } else if (variable.type === 'boolean') {
                defaultValue = defaultValue === 'true' || defaultValue === true
              } else if (variable.type === 'array') {
                defaultValue =
                  typeof defaultValue === 'string'
                    ? JSON.parse(defaultValue)
                    : defaultValue
              } else if (variable.type === 'object') {
                defaultValue =
                  typeof defaultValue === 'string'
                    ? JSON.parse(defaultValue)
                    : defaultValue
              } else if (variable.type === 'daterange') {
                defaultValue = Array.isArray(defaultValue) ? defaultValue : []
              }
            } catch (error) {
              console.warn(
                `Failed to parse default value for variable ${variable.name}:`,
                error
              )
              defaultValue = getVariableTypeDefaultValue(variable.type)
            }
          } else {
            defaultValue = getVariableTypeDefaultValue(variable.type)
          }

          variableData[variable.id] = defaultValue
        } else {
          variableData[variable.id] = formVariables.value[variable.id]
        }
      })

      formVariables.value = variableData
    }
  }

  // 获取变量类型的默认值
  const getVariableTypeDefaultValue = type => {
    switch (type) {
      case 'string':
        return ''
      case 'number':
        return 0
      case 'boolean':
        return false
      case 'date':
        return ''
      case 'daterange':
        return []
      case 'array':
        return []
      case 'object':
        return {}
      default:
        return ''
    }
  }

  // 获取表单元素的实际值（优先从变量绑定获取）
  const getFormChildValue = child => {
    // 如果有变量绑定，从变量中获取值
    if (child.config?.bindVariable) {
      // 如果formVariables中没有这个变量，先初始化它
      if (formVariables.value[child.config.bindVariable] === undefined) {
        // 根据组件类型确定默认值类型
        let defaultValueType = 'string'
        if (child.type === 'datepicker') {
          // 根据日期选择器的类型确定默认值类型
          const dateType = child.config?.type || 'date'
          if (dateType.includes('range')) {
            defaultValueType = 'daterange'
          } else {
            defaultValueType = 'date'
          }
        } else if (child.type === 'select' && child.config?.multiple) {
          defaultValueType = 'array'
        }

        // 从组件配置获取默认值或使用类型默认值
        const defaultValue =
          child.config?.value || getVariableTypeDefaultValue(defaultValueType)
        formVariables.value[child.config.bindVariable] = defaultValue
      }
      return formVariables.value[child.config.bindVariable]
    }
    // 否则从组件配置获取默认值
    return child.config?.value || getVariableTypeDefaultValue('string')
  }

  // 计算属性
  const component = computed(() => props.component || {})

  const chartId = computed(() => {
    if (!component.value || !component.value.id) {
      return 'chart-default'
    }
    return `chart-${component.value.id}`
  })

  // 获取插槽子组件
  const slotChildComponent = computed(() => {
    if (
      !component.value.config?.hasSlotChild ||
      !component.value.config?.slotChildId
    ) {
      return null
    }

    return (
      props.allComponents.find(
        comp => comp.id === component.value.config.slotChildId
      ) || null
    )
  })

  // 检查插槽子组件是否被选中
  const isSlotChildSelected = computed(() => {
    return (
      slotChildComponent.value &&
      props.selectedComponent &&
      slotChildComponent.value.id === props.selectedComponent.id
    )
  })

  // 获取子容器列表
  const childContainers = computed(() => {
    if (
      !component.value.config?.isContainer ||
      !component.value.config?.childrenIds ||
      !Array.isArray(component.value.config.childrenIds)
    ) {
      return []
    }

    const childrenIds = component.value.config.childrenIds
    const foundChildren = childrenIds
      .map(childId => props.allComponents.find(comp => comp.id === childId))
      .filter(Boolean) // 过滤掉 null/undefined
      .filter(child => {
        // 确保子组件确实是当前组件的子容器
        return (
          child.config?.isChildContainer &&
          child.config?.parentId === component.value.id
        )
      })

    return foundChildren
  })

  const isStaticChart = computed(() => {
    if (!component.value || !component.value.type) {
      return false
    }
    return ['pie-chart', 'bar-chart', 'line-chart', 'custom-chart'].includes(
      component.value.type
    )
  })

  // 图表容器样式
  const chartContainerStyle = computed(() => {
    if (!component.value?.size) return {}

    // 如果组件设置了铺满画布，则使用100%宽高
    if (component.value.fullCanvas) {
      return {
        width: '100%',
        height: '100%'
      }
    }

    // 否则使用指定的尺寸，确保数值类型转换
    const width =
      typeof component.value.size.width === 'string' &&
      component.value.size.width.endsWith('%')
        ? component.value.size.width
        : `${Number(component.value.size.width) || 100}px`

    const height =
      typeof component.value.size.height === 'string' &&
      component.value.size.height.endsWith('%')
        ? component.value.size.height
        : `${Number(component.value.size.height) || 100}px`

    return {
      width,
      height
    }
  })

  // DIV容器样式
  const divContainerStyle = computed(() => {
    if (!component.value?.config) return {}

    const config = component.value.config

    const baseStyle = {
      backgroundColor: config.backgroundColor || '#ffffff',
      borderColor: config.borderColor || '#e4e7ed',
      borderWidth: `${config.borderWidth || 1}px`,
      borderStyle: config.borderStyle || 'solid',
      borderRadius: `${config.borderRadius || 4}px`,
      width: '100%',
      height: '100%',
      boxSizing: 'border-box'
    }

    // 如果是容器组，使用flex布局
    if (config.isContainer) {
      return {
        ...baseStyle,
        display: 'flex',
        flexDirection: 'column',
        padding: config.showTitleBar ? '0' : '0'
      }
    }

    // 普通单一容器或子容器
    const containerStyle = {
      ...baseStyle,
      padding: config.showTitleBar ? '0' : `${config.padding || 8}px`
    }

    // 如果有标题栏，不需要设置布局相关的样式
    if (!config.showTitleBar && !config.isChildContainer) {
      containerStyle.display = 'flex'
      containerStyle.alignItems = 'center'
      containerStyle.justifyContent = 'center'
    }

    return containerStyle
  })

  // 标题栏样式
  const titleBarStyle = computed(() => {
    if (!component.value?.config) return {}

    const config = component.value.config
    return {
      backgroundColor: config.titleBackgroundColor || '#f5f5f5',
      color: config.titleTextColor || '#303133',
      fontSize: `${config.titleFontSize || 14}px`,
      height: `${config.titleHeight || 32}px`,
      textAlign: config.titleAlign || 'center',
      lineHeight: `${config.titleHeight || 32}px`,
      borderBottom: '1px solid #e4e7ed',
      padding: '0 12px',
      fontWeight: '500',
      borderTopLeftRadius: `${config.borderRadius || 4}px`,
      borderTopRightRadius: `${config.borderRadius || 4}px`
    }
  })

  // 内容区域样式
  const contentAreaStyle = computed(() => {
    if (!component.value?.config) return {}

    const config = component.value.config
    const titleHeight = config.titleHeight || 32

    return {
      flex: 1,
      height: `calc(100% - ${titleHeight}px)`,
      padding: `${config.padding || 8}px`,
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      borderBottomLeftRadius: `${config.borderRadius || 4}px`,
      borderBottomRightRadius: `${config.borderRadius || 4}px`
    }
  })

  // 子容器区域样式
  const childContainerAreaStyle = computed(() => {
    if (!component.value?.config) return {}

    const config = component.value.config
    const titleHeight = config.titleHeight || 32
    const parentPadding = config.parentPadding || 12

    const baseStyle = {
      display: 'flex',
      gap: '8px',
      padding: `${parentPadding}px`,
      borderBottomLeftRadius: `${config.borderRadius || 4}px`,
      borderBottomRightRadius: `${config.borderRadius || 4}px`
    }

    // 如果有标题栏，需要设置高度
    if (config.showTitleBar) {
      baseStyle.flex = 1
      baseStyle.height = `calc(100% - ${titleHeight}px)`
    } else {
      baseStyle.height = '100%'
    }

    // 根据拆分方向设置 flex 布局
    if (config.direction === 'horizontal') {
      baseStyle.flexDirection = 'row'
    } else {
      baseStyle.flexDirection = 'column'
    }

    return baseStyle
  })

  // 子容器样式
  const childContainerStyle = computed(() => {
    return {
      flex: 1,
      position: 'relative',
      minWidth: '50px', // 设置最小宽度避免被压缩
      minHeight: '50px' // 设置最小高度避免被压缩
    }
  })

  // ECharts相关逻辑
  const chartOptions = computed(() => {
    if (isStaticChart.value) {
      let baseOptions

      // 自定义图表优先使用customOptions
      if (component.value.type === 'custom-chart') {
        baseOptions = cloneDeep(
          component.value.config?.customConfig?.customOptions
        )

        // 如果没有自定义配置，使用默认的options或模板
        if (!baseOptions || Object.keys(baseOptions).length === 0) {
          baseOptions =
            cloneDeep(component.value.config?.options) ||
            getChartTemplate(component.value.type)
        }
      } else {
        // 其他图表类型使用原有逻辑
        baseOptions = cloneDeep(component.value.config?.options)
        if (!baseOptions) {
          baseOptions = getChartTemplate(component.value.type)
        }
      }

      // 自定义图表跳过特定配置，直接使用自定义的options
      if (component.value.type !== 'custom-chart') {
        // 应用柱状图特有配置
        if (
          component.value.type === 'bar-chart' &&
          component.value.config?.barConfig
        ) {
          const displayConfig = {
            showTitle: component.value.config?.showTitle,
            showLegend: component.value.config?.showLegend,
            showTooltip: component.value.config?.showTooltip
          }
          baseOptions = updateBarChartOptions(
            baseOptions,
            component.value.config.barConfig,
            displayConfig
          )
        }

        // 应用折线图特有配置
        if (
          component.value.type === 'line-chart' &&
          component.value.config?.lineConfig
        ) {
          const displayConfig = {
            showTitle: component.value.config?.showTitle,
            showLegend: component.value.config?.showLegend,
            showTooltip: component.value.config?.showTooltip
          }
          baseOptions = updateLineChartOptions(
            baseOptions,
            component.value.config.lineConfig,
            displayConfig
          )
        }

        // 应用饼图特有配置
        if (
          component.value.type === 'pie-chart' &&
          component.value.config?.pieConfig
        ) {
          const displayConfig = {
            showTitle: component.value.config?.showTitle,
            showLegend: component.value.config?.showLegend,
            showTooltip: component.value.config?.showTooltip
          }
          baseOptions = updatePieChartOptions(
            baseOptions,
            component.value.config.pieConfig,
            displayConfig
          )
        }
      }

      // 应用显示配置（对于饼图等其他图表类型）
      if (component.value.config?.showTitle === false) {
        baseOptions.title.show = false
      } else if (component.value.config?.showTitle === true) {
        baseOptions.title.show = true
      }

      if (component.value.config?.showLegend === false) {
        baseOptions.legend.show = false
      } else if (component.value.config?.showLegend === true) {
        baseOptions.legend.show = true
      }

      if (component.value.config?.showTooltip === false) {
        baseOptions.tooltip.show = false
      } else if (component.value.config?.showTooltip === true) {
        baseOptions.tooltip.show = true
      }

      // 自定义图表可以选择是否应用颜色配置（通常不需要，因为颜色在options中已定义）
      if (component.value.type !== 'custom-chart') {
        // 应用颜色配置
        const colorConfig = component.value.config?.colors
        const displayConfig = {
          showTitle: component.value.config?.showTitle,
          showLegend: component.value.config?.showLegend,
          showTooltip: component.value.config?.showTooltip
        }
        if (colorConfig) {
          baseOptions = applyColorConfig(
            baseOptions,
            colorConfig,
            displayConfig,
            component.value.config
          )
        }
      }

      return baseOptions
    }
    return null
  })
  useEcharts(chartRef, chartOptions)

  // API图表相关逻辑
  const apiChartOptions = computed(() => {
    if (component.value.type !== 'api-chart' || !apiData.value) {
      return null
    }
    const {
      chartType = 'bar',
      xField = 'name',
      yField = 'value',
      colors = {}
    } = component.value.config

    // 获取颜色配置
    const getApiChartColors = () => {
      const theme = colors.theme || 'default'
      const colorThemes = {
        default: [
          '#5470c6',
          '#91cc75',
          '#fac858',
          '#ee6666',
          '#73c0de',
          '#3ba272',
          '#fc8452',
          '#9a60b4',
          '#ea7ccc'
        ],
        blue: [
          '#1890ff',
          '#40a9ff',
          '#69c0ff',
          '#91d5ff',
          '#bae7ff',
          '#e6f7ff'
        ],
        green: [
          '#52c41a',
          '#73d13d',
          '#95de64',
          '#b7eb8f',
          '#d9f7be',
          '#f0ffff'
        ],
        red: ['#f5222d', '#ff4d4f', '#ff7875', '#ffa39e', '#ffccc7', '#fff1f0'],
        purple: [
          '#722ed1',
          '#9254de',
          '#b37feb',
          '#d3adf7',
          '#efdbff',
          '#f9f0ff'
        ],
        orange: [
          '#fa8c16',
          '#ffa940',
          '#ffc069',
          '#ffd591',
          '#ffe7ba',
          '#fff7e6'
        ],
        // 渐变色主题
        gradient_ocean: [
          {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: '#667eea' },
              { offset: 1, color: '#764ba2' }
            ]
          },
          {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: '#f093fb' },
              { offset: 1, color: '#f5576c' }
            ]
          },
          {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: '#4facfe' },
              { offset: 1, color: '#00f2fe' }
            ]
          },
          {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: '#43e97b' },
              { offset: 1, color: '#38f9d7' }
            ]
          },
          {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: '#fa709a' },
              { offset: 1, color: '#fee140' }
            ]
          }
        ],
        gradient_sunset: [
          {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: '#ff9a56' },
              { offset: 1, color: '#ff6b6b' }
            ]
          },
          {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: '#ffecd2' },
              { offset: 1, color: '#fcb69f' }
            ]
          },
          {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: '#a8edea' },
              { offset: 1, color: '#fed6e3' }
            ]
          },
          {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: '#ff9a9e' },
              { offset: 1, color: '#fecfef' }
            ]
          },
          {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: '#fad0c4' },
              { offset: 1, color: '#ffd1ff' }
            ]
          }
        ],
        gradient_forest: [
          {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: '#134e5e' },
              { offset: 1, color: '#71b280' }
            ]
          },
          {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: '#5f7c8a' },
              { offset: 1, color: '#a8b8d8' }
            ]
          },
          {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: '#89f7fe' },
              { offset: 1, color: '#66a6ff' }
            ]
          },
          {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: '#48c6ef' },
              { offset: 1, color: '#6f86d6' }
            ]
          },
          {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: '#667eea' },
              { offset: 1, color: '#764ba2' }
            ]
          }
        ],
        gradient_tech: [
          {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: '#667db6' },
              { offset: 1, color: '#0082c8' }
            ]
          },
          {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: '#f12711' },
              { offset: 1, color: '#f5af19' }
            ]
          },
          {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: '#654ea3' },
              { offset: 1, color: '#eaafc8' }
            ]
          },
          {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: '#009ffd' },
              { offset: 1, color: '#2a2a72' }
            ]
          },
          {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: '#8360c3' },
              { offset: 1, color: '#2ebf91' }
            ]
          }
        ]
      }

      if (theme === 'custom') {
        if (colors.gradientMode && colors.customGradients) {
          // 自定义渐变色模式
          return colors.customGradients.map(gradient => {
            return createApiGradientConfig(
              gradient,
              colors.gradientDirection || 'vertical'
            )
          })
        } else if (colors.customColors) {
          // 自定义纯色模式
          return colors.customColors
        }
      }

      return colorThemes[theme] || colorThemes.default
    }

    const options = {
      backgroundColor: colors.backgroundColor || 'transparent',
      title: {
        text: component.value.config.title || 'API图表',
        left: 'center',
        textStyle: {
          color: colors.titleColor || '#333333'
        },
        show: component.value.config?.showTitle !== false
      },
      tooltip: {
        trigger: 'axis',
        backgroundColor: colors.tooltipBg || '#ffffff',
        textStyle: {
          color: colors.tooltipText || '#333333'
        },
        show: component.value.config?.showTooltip !== false
      },
      legend: (() => {
        const legendConfig = {
          textStyle: {
            color: colors.legendColor || '#333333'
          },
          show: component.value.config?.showLegend !== false
        }

        // 应用图例位置
        if (component.value.config?.legendPosition) {
          const position = component.value.config.legendPosition
          if (position === 'top') {
            legendConfig.top = '10px'
            legendConfig.left = 'center'
          } else if (position === 'bottom') {
            legendConfig.bottom = '10px'
            legendConfig.left = 'center'
          } else if (position === 'left') {
            legendConfig.top = 'center'
            legendConfig.left = '10px'
          } else if (position === 'right') {
            legendConfig.top = 'center'
            legendConfig.right = '10px'
          }
        }

        // 应用图例方向
        if (component.value.config?.legendOrient) {
          legendConfig.orient = component.value.config.legendOrient
        }

        return legendConfig
      })(),
      xAxis: {
        type: 'category',
        data: apiData.value.map(item => item[xField] ?? '未知'),
        axisLine: {
          lineStyle: {
            color: colors.xAxisColor || '#666666'
          }
        },
        axisLabel: {
          color: colors.xAxisColor || '#666666'
        }
      },
      yAxis: {
        type: 'value',
        axisLine: {
          lineStyle: {
            color: colors.yAxisColor || '#666666'
          }
        },
        axisLabel: {
          color: colors.yAxisColor || '#666666'
        },
        splitLine: {
          lineStyle: {
            color: colors.gridColor || '#e0e0e0'
          }
        }
      },
      color: getApiChartColors(),
      series: [
        {
          type: chartType,
          data: apiData.value.map(item => item[yField] ?? 0)
        }
      ]
    }

    return options
  })
  useEcharts(apiChartRef, apiChartOptions)

  // 监听组件变化
  watch(
    () => props.component,
    async newComponent => {
      if (newComponent) {
        if (newComponent.dataSource === 'api') {
          fetchApiData()
        }
      }
    },
    { deep: true, immediate: true }
  )

  // 监听容器尺寸变化
  const resizeObserver = ref(null)

  onMounted(() => {
    if (component.value && component.value.dataSource === 'api') {
      fetchApiData()
    }

    // 如果是图表组件，监听容器尺寸变化
    if (isStaticChart.value || component.value.type === 'api-chart') {
      resizeObserver.value = new ResizeObserver(() => {
        if (chartRef.value) {
          const chart = echarts.getInstanceByDom(chartRef.value)
          chart?.resize()
        }
        if (apiChartRef.value) {
          const chart = echarts.getInstanceByDom(apiChartRef.value)
          chart?.resize()
        }
      })

      if (chartRef.value) {
        resizeObserver.value.observe(chartRef.value)
      }
      if (apiChartRef.value) {
        resizeObserver.value.observe(apiChartRef.value)
      }
    }
  })

  onUnmounted(() => {
    // 清理尺寸监听器
    if (resizeObserver.value) {
      resizeObserver.value.disconnect()
      resizeObserver.value = null
    }
  })

  // 获取API数据
  const fetchApiData = async () => {
    if (
      !component.value ||
      !component.value.config ||
      component.value.dataSource !== 'api'
    ) {
      return
    }

    const {
      apiUrl,
      method = 'GET',
      headers = {},
      dataPath
    } = component.value.config

    if (!apiUrl) {
      apiData.value = []
      return
    }

    loading.value = true

    try {
      const response = await fetch(apiUrl, {
        method,
        headers: {
          'Content-Type': 'application/json',
          ...headers
        }
      })

      const data = await response.json()

      // 根据dataPath提取数据
      if (dataPath) {
        apiData.value = getNestedValue(data, dataPath)
      } else {
        apiData.value = data
      }
      // 数据获取后，依赖apiChartOptions的computed属性会自动更新，useEcharts hook会处理图表渲染
    } catch (error) {
      console.error('获取API数据失败:', error)
      apiData.value = []
    } finally {
      loading.value = false
    }
  }

  // 格式化指标值
  const formatIndicatorValue = value => {
    if (!component.value || !component.value.config) {
      return value || '--'
    }
    const { prefix = '', suffix = '', unit = '' } = component.value.config

    if (typeof value === 'number') {
      return `${prefix}${value.toLocaleString()}${unit}${suffix}`
    }

    return `${prefix}${value || '--'}${unit}${suffix}`
  }

  // 创建API图表渐变色配置
  const createApiGradientConfig = (gradient, direction) => {
    const { startColor, endColor } = gradient

    // 根据方向设置渐变参数
    let gradientConfig = {
      type: 'linear',
      colorStops: [
        { offset: 0, color: startColor },
        { offset: 1, color: endColor }
      ]
    }

    switch (direction) {
      case 'horizontal':
        gradientConfig.x = 0
        gradientConfig.y = 0
        gradientConfig.x2 = 1
        gradientConfig.y2 = 0
        break
      case 'vertical':
        gradientConfig.x = 0
        gradientConfig.y = 0
        gradientConfig.x2 = 0
        gradientConfig.y2 = 1
        break
      case 'diagonal1': // 对角线 ↘
        gradientConfig.x = 0
        gradientConfig.y = 0
        gradientConfig.x2 = 1
        gradientConfig.y2 = 1
        break
      case 'diagonal2': // 对角线 ↗
        gradientConfig.x = 0
        gradientConfig.y = 1
        gradientConfig.x2 = 1
        gradientConfig.y2 = 0
        break
      case 'radial': // 径向渐变
        gradientConfig = {
          type: 'radial',
          x: 0.5,
          y: 0.5,
          r: 0.5,
          colorStops: [
            { offset: 0, color: startColor },
            { offset: 1, color: endColor }
          ]
        }
        break
      default:
        // 默认垂直渐变
        gradientConfig.x = 0
        gradientConfig.y = 0
        gradientConfig.x2 = 0
        gradientConfig.y2 = 1
        break
    }

    return gradientConfig
  }

  // 获取嵌套对象的值
  const getNestedValue = (obj, path) => {
    if (!obj || !path) return null
    return path.split('.').reduce((current, key) => {
      return current && current[key] !== undefined ? current[key] : null
    }, obj)
  }

  // 按钮点击处理函数
  const handleButtonClick = async () => {
    // 检查新的 eventBinding 结构
    const eventBinding = component.value?.config?.eventBinding

    if (!eventBinding || eventBinding.type === 'none') {
      return
    }

    switch (eventBinding.type) {
      case 'method':
        await executeMethodCall(eventBinding, component.value)
        break
      case 'custom':
        await executeCustomEvent(eventBinding, component.value)
        break
      default:
        ElMessage.info('按钮事件类型未识别')
    }
  }

  // 执行方法调用
  const executeMethodCall = async (eventBinding, buttonComponent) => {
    try {
      if (!eventBinding.methodName) {
        ElMessage.warning('请先选择要调用的方法')
        return
      }

      // 获取全局方法代码
      const globalMethods = props.schema?.methods || ''

      if (!globalMethods) {
        ElMessage.warning('没有找到全局方法定义')
        return
      }

      // 创建执行上下文
      const context = createEventContext(buttonComponent)

      // 解析方法参数
      const methodParams = eventBinding.methodParams || ''

      // 创建方法调用代码
      const methodCallCode = `
        // 定义全局方法
        ${globalMethods}
        
        // 调用指定方法
        return ${eventBinding.methodName}(${methodParams});
      `

      // 创建执行函数
      const executeCode = new Function(
        'context',
        'axios',
        'dayjs',
        'ElMessage',
        'ElMessageBox',
        'console',
        `
        // 绑定this上下文
        const self = context;
        
        // 执行方法调用
        return (async function() {
          ${methodCallCode}
        }).call(self);
        `
      )

      // 执行方法调用
      await executeCode(
        context,
        context.axios,
        context.dayjs,
        context.ElMessage,
        context.ElMessageBox,
        context.console
      )
    } catch (error) {
      console.error('方法调用失败:', error)
      console.error('错误堆栈:', error.stack)
      ElMessage.error(`方法调用失败: ${error.message}`)
    }
  }

  // 执行自定义事件
  const executeCustomEvent = async (eventBinding, buttonComponent) => {
    try {
      if (!eventBinding.eventCode) {
        ElMessage.warning('请先配置自定义事件代码')
        return
      }

      // 创建执行上下文
      const context = createEventContext(buttonComponent)

      // 创建一个更简单的执行函数
      const executeCode = new Function(
        'context',
        'axios',
        'dayjs',
        'ElMessage',
        'ElMessageBox',
        'console',
        `
        // 绑定this上下文
        const self = context;
        
        // 执行用户代码
        return (async function() {
          ${eventBinding.eventCode}
        }).call(self);
        `
      )

      // 执行自定义事件代码
      await executeCode(
        context,
        context.axios,
        context.dayjs,
        context.ElMessage,
        context.ElMessageBox,
        context.console
      )
    } catch (error) {
      console.error('自定义事件执行失败:', error)
      console.error('错误堆栈:', error.stack)
      ElMessage.error(`自定义事件执行失败: ${error.message}`)
    }
  }

  // 创建事件执行上下文
  const createEventContext = buttonComponent => {
    // 导入必要的工具库
    const axios =
      window.axios ||
      (() => {
        ElMessage.error('axios 未加载，请确保已引入 axios 库')
        return null
      })()

    const dayjs =
      window.dayjs ||
      (() => {
        ElMessage.error('dayjs 未加载，请确保已引入 dayjs 库')
        return null
      })()

    // 使用传入的按钮组件或默认的component
    const targetComponent = buttonComponent || component.value

    return {
      // 工具库
      axios,
      dayjs,
      ElMessage,
      ElMessageBox,
      console,

      // 组件变量
      variables: {
        formData: formData.value || {},
        buttonConfig: targetComponent?.config || {},
        componentId: targetComponent?.id || ''
      },

      // 按钮配置
      buttonConfig: targetComponent?.config || {},

      // 组件ID
      componentId: targetComponent?.id || ''
    }
  }

  // 表单容器样式
  const formContainerStyle = computed(() => {
    if (!component.value?.config) return {}

    const config = component.value.config
    return {
      backgroundColor: config.backgroundColor || '#ffffff',
      borderColor: config.borderColor || '#e4e7ed',
      borderWidth: `${config.borderWidth || 1}px`,
      borderStyle: config.borderStyle || 'solid',
      borderRadius: `${config.borderRadius || 4}px`,
      width: '100%',
      height: '100%',
      boxSizing: 'border-box',
      overflow: 'auto'
    }
  })

  const formContainerTitleStyle = computed(() => {
    return {
      padding: '12px 16px',
      borderBottom: '1px solid #e4e7ed',
      fontSize: '16px',
      fontWeight: '500',
      color: '#303133',
      backgroundColor: '#f8f9fa'
    }
  })

  const formContainerContentStyle = computed(() => {
    const config = component.value?.config || {}
    const showTitle = config.showTitle !== false
    return {
      padding: `${config.padding || 16}px`,
      height: showTitle ? 'calc(100% - 49px)' : '100%',
      overflow: 'hidden'
    }
  })

  // 获取表单子项样式
  const getFormChildStyle = () => {
    const config = component.value?.config || {}
    const layout = config.layout || 'vertical'
    const gap = config.gap || 16

    let style = {
      marginBottom: layout === 'vertical' ? `${gap}px` : '0'
    }

    if (layout === 'horizontal') {
      style.display = 'inline-block'
      style.marginRight = `${gap}px`
      style.verticalAlign = 'top'
    } else if (layout === 'grid') {
      const columns = config.gridColumns || 2
      const gridGap = config.gridGap || 16
      style.display = 'inline-block'
      style.width = `calc(${100 / columns}% - ${gridGap}px)`
      style.marginRight = `${gridGap}px`
      style.marginBottom = `${gridGap}px`
      style.verticalAlign = 'top'
    }

    return style
  }

  // 获取表单项组件
  const getFormItemComponent = () => {
    return ElFormItem
  }

  // 获取表单控件组件
  const getFormControlComponent = type => {
    const componentMap = {
      input: ElInput,
      select: ElSelect,
      datepicker: ElDatePicker,
      button: ElButton
    }
    return componentMap[type] || ElInput
  }

  // 获取表单控件属性
  const getFormControlProps = child => {
    // 优先使用子组件的size，然后是容器的size，最后是默认值
    const containerSize = component.value?.config?.size || 'default'
    const childSize = child.config?.size || containerSize

    const baseProps = {
      modelValue: getFormChildValue(child),
      placeholder: child.config?.placeholder || '',
      disabled: child.config?.disabled || false,
      size: childSize
    }

    switch (child.type) {
      case 'input':
        return {
          ...baseProps,
          clearable: child.config?.clearable !== false,
          showPassword: child.config?.showPassword || false,
          maxlength: child.config?.maxlength,
          showWordLimit: child.config?.showWordLimit || false
        }
      case 'select':
        return {
          ...baseProps,
          clearable: child.config?.clearable !== false,
          filterable: child.config?.filterable || false,
          multiple: child.config?.multiple || false
        }
      case 'datepicker':
        return {
          ...baseProps,
          type: child.config?.type || 'date',
          format: child.config?.format || 'YYYY-MM-DD',
          valueFormat: child.config?.valueFormat || 'YYYY-MM-DD',
          clearable: child.config?.clearable !== false,
          rangeSeparator: child.config?.rangeSeparator || '至',
          startPlaceholder: child.config?.startPlaceholder || '开始日期',
          endPlaceholder: child.config?.endPlaceholder || '结束日期',
          style: { width: '100%' },
          'onUpdate:modelValue': value => handleFormChildChange(child, value)
        }
      case 'button':
        return {
          type: child.config?.type || 'primary',
          size: childSize,
          disabled: child.config?.disabled || false,
          loading: child.config?.loading || false,
          plain: child.config?.plain || false,
          round: child.config?.round || false,
          circle: child.config?.circle || false,
          color: child.config?.color || ''
        }
      default:
        return baseProps
    }
  }

  // 表单子组件事件处理
  const handleFormChildInput = (child, value) => {
    // 如果有变量绑定，更新变量值
    if (child.config?.bindVariable) {
      formVariables.value[child.config.bindVariable] = value
    } else {
      // 如果没有变量绑定，更新组件配置的值
      if (child.config) {
        child.config.value = value
      }
      // 通知父组件数据已更改，以便保存到schema
      emit('update-component', child)
    }
  }

  const handleFormChildChange = (child, value) => {
    // 如果有变量绑定，更新变量值
    if (child.config?.bindVariable) {
      // 强制触发响应式更新
      const variableId = child.config.bindVariable
      // 使用 Vue 3 的响应式 API 强制更新
      formVariables.value = {
        ...formVariables.value,
        [variableId]: value
      }

      // 强制重新渲染
      nextTick(() => {
        // 变量更新完成
      })
    } else {
      // 如果没有变量绑定，更新组件配置的值
      if (child.config) {
        child.config.value = value
      }

      // 通知父组件数据已更改，以便保存到schema
      emit('update-component', child)
    }
  }

  const handleFormChildClick = async child => {
    if (child.type === 'button') {
      // 检查新的 eventBinding 结构
      const eventBinding = child.config?.eventBinding

      if (!eventBinding || eventBinding.type === 'none') {
        return
      }

      switch (eventBinding.type) {
        case 'method':
          await executeMethodCall(eventBinding, child)
          break
        case 'custom':
          await executeCustomEvent(eventBinding, child)
          break
        default:
          ElMessage.info('按钮事件类型未识别')
      }
    }
  }

  // 表单容器拖拽处理
  const handleFormContainerDragOver = event => {
    event.preventDefault()
    event.dataTransfer.dropEffect = 'copy'
  }

  const handleFormContainerDrop = event => {
    event.preventDefault()
    // 这里需要与父组件通信来处理拖拽放置
  }

  // DIV容器拖拽处理
  const handleDivContainerDragOver = event => {
    if (props.isPreview) return
    event.preventDefault()
    event.dataTransfer.dropEffect = 'copy'
  }

  const handleDivContainerDrop = event => {
    if (props.isPreview) return
    event.preventDefault()
    // 这里需要与父组件通信来处理拖拽放置
    // 拖拽事件会冒泡到EditorCanvas处理
  }

  // 插槽子组件点击处理
  const handleSlotChildClick = slotChild => {
    if (props.isPreview) return
    // 选中插槽子组件
    emit('select-component', slotChild)
  }

  // 删除插槽子组件处理
  const handleSlotChildDelete = () => {
    if (props.isPreview) return
    // 发送删除事件给父组件
    emit('delete-component', slotChildComponent.value.id)
  }

  // 表单子组件选中处理
  const selectFormChild = child => {
    // 取消表单行的选中状态
    emit('select-form-row', null)
    emit('select-form-child', child)
  }

  // 表单子组件编辑处理
  const editFormChild = child => {
    emit('update-form-child', child)
  }

  // 表单子组件移动处理
  const moveFormChild = (index, direction) => {
    emit('reorder-form-children', index, direction)
  }

  // 表单子组件删除处理
  const deleteFormChild = index => {
    emit('delete-form-child', index)
  }

  // 表单容器点击处理
  const handleFormContainerClick = () => {
    // 点击表单容器空白区域，清除所有选中状态
    if (!props.isPreview) {
      emit('select-form-child', null)
      emit('select-form-row', null)
    }
  }

  // 表单行样式
  const getFormRowStyle = row => {
    return {
      width: '100%',
      height: `${row.height || 60}px`,
      minHeight: '40px',
      backgroundColor: row.backgroundColor || 'transparent',
      borderColor: row.borderColor || '#e4e7ed',
      borderWidth: `${row.borderWidth || 0}px`,
      borderStyle: row.borderStyle || 'solid',
      padding:
        row.columns && row.columns.length > 0 ? '0' : `${row.padding || 8}px`,
      marginBottom: '8px',
      boxSizing: 'border-box',
      position: 'relative',
      display: 'flex',
      alignItems: 'stretch',
      borderRadius: '4px'
    }
  }

  // 表单行选中处理
  const selectFormRow = row => {
    // 取消表单子组件的选中状态
    emit('select-form-child', null)
    emit('select-form-row', row)
  }

  // 表单行编辑处理
  const editFormRow = row => {
    emit('update-form-row', row)
  }

  // 表单行移动处理
  const moveFormRow = (index, direction) => {
    emit('reorder-form-rows', index, direction)
  }

  // 表单行删除处理
  const deleteFormRow = index => {
    emit('delete-form-row', index)
  }

  // 表单行高度拖拽调整
  const startRowResize = (row, event) => {
    event.preventDefault()

    const startY = event.clientY
    const startHeight = row.height || 60

    const handleMouseMove = e => {
      const deltaY = e.clientY - startY
      const newHeight = Math.max(40, startHeight + deltaY)

      // 直接更新行高度
      row.height = newHeight
      emit('update-form-row', row)
    }

    const handleMouseUp = () => {
      document.removeEventListener('mousemove', handleMouseMove)
      document.removeEventListener('mouseup', handleMouseUp)
    }

    document.addEventListener('mousemove', handleMouseMove)
    document.addEventListener('mouseup', handleMouseUp)
  }

  // 表单行右键菜单处理
  const handleFormRowContextMenu = (row, event) => {
    event.preventDefault()
    // 先选中行
    selectFormRow(row)
    // 发出右键菜单事件
    emit('form-row-context-menu', {
      row: row,
      x: event.clientX,
      y: event.clientY
    })
  }

  // 表单列右键菜单处理
  const handleFormColumnContextMenu = (column, row, event) => {
    event.preventDefault()
    // 先选中列
    selectFormColumn(column, row)
    // 发出右键菜单事件
    emit('form-column-context-menu', {
      column: column,
      row: row,
      formContainer: component.value,
      x: event.clientX,
      y: event.clientY
    })
  }

  // 表单列拖拽处理
  const dragOverColumn = ref(null)
  const draggedFormChild = ref(null)
  const draggedFormChildSource = ref(null) // 记录拖拽源信息

  const handleColumnDragOver = (column, row, event) => {
    if (props.isPreview) return

    event.preventDefault()
    event.stopPropagation()

    // 根据拖拽内容设置不同的效果
    if (draggedFormChild.value) {
      event.dataTransfer.dropEffect = 'move'
    } else {
      event.dataTransfer.dropEffect = 'copy'
    }

    // 设置拖拽悬停状态
    dragOverColumn.value = column
  }

  const handleColumnDragLeave = event => {
    if (props.isPreview) return

    // 只有当拖拽真正离开列时才清除悬停状态
    const rect = event.currentTarget.getBoundingClientRect()
    const x = event.clientX
    const y = event.clientY

    if (x < rect.left || x > rect.right || y < rect.top || y > rect.bottom) {
      dragOverColumn.value = null
    }
  }

  const handleColumnDrop = (column, row, event) => {
    if (props.isPreview) return

    event.preventDefault()
    event.stopPropagation()

    // 清除拖拽悬停状态
    dragOverColumn.value = null

    if (draggedFormChild.value && draggedFormChildSource.value) {
      // 处理表单组件在列间的拖拽
      emit('move-form-child-between-columns', {
        sourceColumn: draggedFormChildSource.value.column,
        sourceRow: draggedFormChildSource.value.row,
        targetColumn: column,
        targetRow: row,
        formChild: draggedFormChild.value,
        formContainer: component.value
      })
    } else {
      // 处理从组件库拖拽到列的事件
      emit('drop-to-column', {
        column: column,
        row: row,
        formContainer: component.value
      })
    }
  }

  // 表单列内子组件操作处理
  const deleteColumnFormChild = (column, row, childIndex) => {
    emit('delete-column-form-child', {
      column: column,
      row: row,
      childIndex: childIndex,
      formContainer: component.value
    })
  }

  // 表单列选中处理
  const selectFormColumn = (column, row) => {
    emit('select-form-column', {
      column: column,
      row: row,
      formContainer: component.value
    })
  }

  // 处理行点击选中
  const handleRowClick = row => {
    // 只在非预览模式下响应点击
    if (!props.isPreview) {
      selectFormRow(row)
    }
  }

  // 获取列的span值
  const getColumnSpan = (row, colIndex) => {
    // 如果行有配置的列span分配
    if (row.columnSpans && Array.isArray(row.columnSpans)) {
      // 确保有足够的配置
      if (colIndex < row.columnSpans.length) {
        return row.columnSpans[colIndex]
      }
      // 如果配置不够，使用平均分配
      const remainingSpan =
        24 - row.columnSpans.reduce((sum, span) => sum + span, 0)
      const remainingCols = row.columns.length - row.columnSpans.length
      return remainingCols > 0 ? Math.floor(remainingSpan / remainingCols) : 1
    }

    // 如果没有配置，平均分配24格
    const totalCols = row.columns ? row.columns.length : 1
    return Math.floor(24 / totalCols)
  }

  // 表单组件拖拽处理
  const handleFormChildDragStart = (child, column, row, event) => {
    if (props.isPreview) return

    draggedFormChild.value = child
    draggedFormChildSource.value = {
      column: column,
      row: row
    }

    // 设置拖拽数据
    event.dataTransfer.setData(
      'application/json',
      JSON.stringify({
        type: 'form-child',
        child: child,
        sourceColumn: column,
        sourceRow: row
      })
    )

    event.dataTransfer.effectAllowed = 'move'
  }

  const handleFormChildDragEnd = () => {
    draggedFormChild.value = null
    draggedFormChildSource.value = null
  }

  // 分离列中的按钮和其他表单元素
  const separateButtonsAndOthers = children => {
    if (!children || !Array.isArray(children)) {
      return { buttons: [], others: [] }
    }

    const buttons = children.filter(child => child.type === 'button')
    const others = children.filter(child => child.type !== 'button')

    return { buttons, others }
  }

  // 调整位置相关状态
  const adjustPositionMode = ref(false)
  const componentToAdjust = ref(null)
  const sourceColumn = ref(null)
  const sourceRow = ref(null)

  // 开始调整位置
  const startAdjustPosition = (component, column, row) => {
    componentToAdjust.value = component
    sourceColumn.value = column
    sourceRow.value = row
    adjustPositionMode.value = true
    // 发送事件到父组件
    emit('start-adjust-position', { component, column, row })
  }

  // 点击列容器进行调整
  const handleColumnClickForAdjust = (targetColumn, targetRow) => {
    if (adjustPositionMode.value && componentToAdjust.value) {
      emit('column-click-for-adjust', {
        sourceComponent: componentToAdjust.value,
        sourceColumn: sourceColumn.value,
        sourceRow: sourceRow.value,
        targetColumn: targetColumn,
        targetRow: targetRow
      })
      // 重置状态
      adjustPositionMode.value = false
      componentToAdjust.value = null
      sourceColumn.value = null
      sourceRow.value = null
    }
  }

  // 监听组件变化，自动初始化表单变量
  watch(
    () => component.value,
    newComponent => {
      if (newComponent && newComponent.type === 'form-container') {
        initFormVariables()
      }
    },
    { immediate: true, deep: true }
  )

  // 组件挂载时初始化
  onMounted(() => {
    if (component.value.type === 'form-container') {
      initFormVariables()
    }
  })
</script>

<style scoped>
  .render-component {
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .render-component.full-canvas-mode {
    display: block;
    align-items: unset;
    justify-content: unset;
  }

  .render-component.full-canvas-mode .chart-container,
  .render-component.full-canvas-mode .api-chart-container {
    width: 100% !important;
    height: 100% !important;
  }

  .render-component.full-canvas-mode .chart-content {
    width: 100% !important;
    height: 100% !important;
  }

  .chart-container,
  .api-table-container,
  .api-indicator-container,
  .api-chart-container,
  .div-container {
    position: relative;
    overflow: hidden;
  }

  .chart-content {
    width: 100%;
    height: 100%;
    position: relative;
    box-sizing: border-box;
  }

  .api-table {
    width: 100%;
  }

  .indicator-content {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
  }

  .indicator-value {
    font-size: 32px;
    font-weight: bold;
    color: #409eff;
    text-align: center;
  }

  .loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
  }

  .loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #409eff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }

  .div-container-content {
    font-size: 14px;
    color: #606266;
    text-align: center;
    line-height: 1.5;
    word-break: break-word;
  }

  .div-container-with-title {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .div-title-bar {
    flex-shrink: 0;
    user-select: none;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .div-content-area {
    font-size: 14px;
    color: #606266;
    text-align: center;
    line-height: 1.5;
    word-break: break-word;
    box-sizing: border-box;
  }

  .child-container {
    position: relative;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    transition: all 0.2s ease;
  }

  .child-container:hover {
    border-color: #409eff;
    box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
  }

  .unknown-component,
  .empty-component {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .unknown-content,
  .empty-content {
    text-align: center;
    color: #909399;
  }

  .unknown-content p,
  .empty-content p {
    margin: 8px 0 0 0;
    font-size: 14px;
  }

  .slot-child-component {
    width: 100%;
    height: 100%;
  }

  .slot-child-component .chart-container,
  .slot-child-component .api-chart-container {
    width: 100% !important;
    height: 100% !important;
  }

  .slot-child-component .chart-content {
    width: 100% !important;
    height: 100% !important;
  }

  /* 表单组件样式 */
  .form-component {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 16px;
    box-sizing: border-box;
  }

  .form-item {
    width: 100%;
    margin-bottom: 0;
  }

  .input-component .el-input,
  .select-component .el-select,
  .datepicker-component .el-date-editor {
    width: 100%;
  }

  .button-component {
    align-items: center;
    justify-content: center;
  }

  .button-component .el-button {
    min-width: 80px;
  }

  /* 表单容器样式 */
  .form-container {
    position: relative;
    border: 2px dashed transparent;
    transition: all 0.2s;
  }

  .form-container:hover {
    border-color: #409eff;
    background-color: rgba(64, 158, 255, 0.02);
  }

  .form-container-title {
    user-select: none;
    border-top-left-radius: inherit;
    border-top-right-radius: inherit;
  }

  .form-container-content {
    position: relative;
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .form-container-form {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    position: relative; /* 为绝对定位的行指示器提供定位上下文 */
    box-sizing: border-box;
    padding-left: 40px; /* 为行选择器留出空间 */
  }

  .form-container-form-preview {
    padding-left: 0; /* 预览模式下不需要左边距 */
  }

  .form-child-item {
    transition: all 0.2s;
  }

  .form-child-form-item {
    margin-bottom: 0;
  }

  /* 表单子组件交互样式 */
  .form-child-item {
    position: relative;
    border: 2px solid transparent;
    border-radius: 4px;
    padding: 8px;
    cursor: pointer;
  }

  .form-child-item:hover:not(.form-child-preview) {
    border-color: #409eff;
    background-color: rgba(64, 158, 255, 0.05);
  }

  .form-child-selected {
    border-color: #409eff !important;
    background-color: rgba(64, 158, 255, 0.1) !important;
    box-shadow: 0 0 0 1px #409eff;
  }

  .form-child-preview {
    cursor: default;
  }

  .form-child-actions {
    position: absolute;
    top: 4px;
    right: 4px;
    z-index: 10;
    opacity: 0;
    transition: opacity 0.2s;
  }

  .form-child-item:hover .form-child-actions {
    opacity: 1;
  }

  .form-child-selected .form-child-actions {
    opacity: 1;
  }

  .action-btn {
    width: 24px;
    height: 24px;
    padding: 0;
    border-radius: 50%;
    background: #409eff;
    color: white;
    border: none;
  }

  .action-btn:hover {
    background: #337ecc;
  }

  .action-menu {
    display: flex;
    flex-direction: column;
    gap: 4px;
  }

  .menu-item {
    width: 100%;
    justify-content: flex-start;
    padding: 8px 12px;
    border-radius: 4px;
  }

  .menu-item:hover {
    background-color: #f5f7fa;
  }

  .delete-btn {
    color: #f56c6c;
  }

  .delete-btn:hover {
    background-color: #fef0f0;
    color: #f56c6c;
  }

  /* 表单容器样式 */
  .form-container-empty {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    color: #909399;
    font-size: 16px;
    font-weight: 500;
    padding: 40px 20px;
    border: 2px dashed #e4e7ed;
    border-radius: 4px;
    background-color: #fafafa;
    min-height: 200px;
    box-sizing: border-box;
  }

  .form-container-hint {
    text-align: center;
    color: #c0c4cc;
    font-size: 12px;
    padding: 8px 20px;
    margin-top: auto;
    opacity: 0.8;
    font-style: italic;
  }

  /* 网格布局适配 */
  .form-container[data-layout='grid'] .form-child-item:nth-child(2n) {
    margin-right: 0;
  }

  .form-container[data-layout='horizontal'] .form-child-item:last-child {
    margin-right: 0;
  }

  /* 按钮组件在表单容器中的特殊样式 */
  .form-child-item .form-child-form-item:has(.el-button) {
    display: flex;
    align-items: center;
    justify-content: flex-start;
  }

  /* 垂直布局时按钮居左对齐 */
  .form-container[data-layout='vertical']
    .form-child-item
    .form-child-form-item:has(.el-button) {
    justify-content: flex-start;
  }

  /* 水平布局时按钮居中对齐 */
  .form-container[data-layout='horizontal']
    .form-child-item
    .form-child-form-item:has(.el-button) {
    justify-content: center;
  }

  /* 网格布局时按钮居中对齐 */
  .form-container[data-layout='grid']
    .form-child-item
    .form-child-form-item:has(.el-button) {
    justify-content: center;
  }

  /* 表单行样式 */
  .form-row {
    position: relative;
    border: 1px dashed transparent;
    border-radius: 4px;
    transition: all 0.2s;
    margin-bottom: 8px;
    width: 100%;
    padding: 2px;
    box-sizing: border-box;
  }

  /* 只在非预览模式下显示选中样式 */
  .form-row:not(.form-row-preview) {
    border-bottom: 3px solid #e4e7ed; /* 底边指示器 */
    cursor: pointer;
  }

  /* 为行添加点击区域 */
  .form-row:not(.form-row-preview)::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -3px; /* 扩展到底边 */
    z-index: 0;
    pointer-events: auto;
  }

  .form-row-hover:hover:not(.form-row-preview) {
    border-bottom-color: #409eff;
    background-color: rgba(64, 158, 255, 0.05);
  }

  .form-row-selected:not(.form-row-preview) {
    border-bottom-color: #409eff !important;
    background-color: rgba(64, 158, 255, 0.1) !important;
    box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
  }

  .form-row-preview {
    cursor: default;
    border: none; /* 预览模式下不显示边框 */
    margin-bottom: 8px; /* 预览模式下正常间距 */
    padding: 0; /* 预览模式下无内边距 */
  }

  /* 行选择器样式 */
  .form-row-selector {
    position: absolute;
    left: -32px;
    top: 50%;
    transform: translateY(-50%);
    width: 28px;
    height: 28px;
    background: #f0f2f5;
    border: 2px solid #e4e7ed;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s;
    z-index: 5;
    user-select: none;
  }

  .form-row-selector:hover {
    background: #409eff;
    border-color: #409eff;
    color: white;
    transform: translateY(-50%) scale(1.1);
  }

  .form-row-selected .form-row-selector {
    background: #409eff;
    border-color: #409eff;
    color: white;
  }

  .row-number {
    font-size: 12px;
    font-weight: bold;
    line-height: 1;
  }

  .form-row-actions {
    position: absolute;
    top: -8px; /* 向上移动到行外部，避免与列内操作按钮重叠 */
    right: 4px;
    z-index: 15; /* 提高z-index确保在所有列操作按钮之上 */
    opacity: 0;
    transition: opacity 0.2s;
  }

  .form-row:hover .form-row-actions {
    opacity: 1;
  }

  .form-row-selected .form-row-actions {
    opacity: 1;
  }

  .form-row-content {
    width: 100%;
    height: auto;
    display: flex;
    align-items: stretch; /* 让所有列保持相同高度 */
    gap: 0;
    padding: 2px 2px 4px 2px; /* 减少内边距 */
    box-sizing: border-box;
    position: relative;
    z-index: 1; /* 确保内容在点击区域之上 */
    min-height: 48px; /* 调整基础高度 */
  }

  .form-row-child {
    flex: 1;
    min-width: 0;
  }

  .form-row-empty {
    text-align: center;
    color: #909399;
    font-size: 14px;
    padding: 16px 8px;
    border: 1px dashed #e4e7ed;
    border-radius: 4px;
    background-color: #fafafa;
    width: 100%;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
    box-sizing: border-box;
  }

  .form-row-empty:hover {
    border-color: #409eff;
    background-color: rgba(64, 158, 255, 0.05);
    color: #409eff;
  }

  .form-row-empty::after {
    content: '（点击选中行）';
    position: absolute;
    bottom: 8px;
    right: 12px;
    font-size: 10px;
    color: #c0c4cc;
    opacity: 0;
    transition: opacity 0.2s ease;
    pointer-events: none;
  }

  .form-row-empty:hover::after {
    opacity: 1;
  }

  /* 行高度调整手柄 */
  .row-resize-handle {
    position: absolute;
    bottom: -3px;
    left: 0;
    right: 0;
    height: 6px;
    background-color: #409eff;
    border-radius: 3px;
    cursor: ns-resize;
    opacity: 0.7;
    z-index: 10;
  }

  .row-resize-handle:hover {
    opacity: 1;
    background-color: #337ecc;
  }

  /* 表单行布局样式 */
  .form-row-layout {
    width: 100%;
  }

  /* 表单列样式 */
  .form-column {
    display: flex;
    flex-direction: column;
    height: 100%; /* 填充父容器高度 */
    background-color: transparent;
    transition: all 0.2s;
    box-sizing: border-box;
  }

  .form-column-inner {
    display: flex;
    flex-direction: column;
    height: 100%; /* 填充父容器高度 */
    background-color: transparent;
    transition: all 0.2s;
    box-sizing: border-box;
    border: 1px dashed #d9d9d9;
    border-radius: 4px;
    padding: 4px; /* 减少内边距，避免内容超出 */
    overflow: hidden; /* 确保内容不会超出边界 */
  }

  .form-column-inner:hover:not(.form-column-preview) {
    border-color: #409eff !important;
    background-color: rgba(64, 158, 255, 0.02);
  }

  /* 预览模式下的列容器样式 */
  .form-column-preview .form-column-inner {
    border: none !important;
    background-color: transparent !important;
    padding: 0 !important;
  }

  /* 表单列内容样式 */
  .form-column-child {
    transition: all 0.2s;
    margin-bottom: 8px;
    display: flex;
    align-items: center; /* 垂直居中对齐 */
    min-height: 32px; /* 最小高度保持一致 */
  }

  .form-column-child:last-child {
    margin-bottom: 0;
  }

  .form-column-child .form-child-form-item {
    width: 100%;
    margin: 0; /* 重置margin，避免额外间距 */
  }

  /* 表单列空状态样式 */
  .form-column-empty {
    text-align: center;
    color: #c0c4cc;
    font-size: 12px;
    padding: 8px 4px;
    border: none;
    border-radius: 0;
    background-color: transparent;
    cursor: pointer;
    transition: all 0.2s ease;
    min-height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    width: 100%;
    margin: 0;
  }

  .form-column-empty:hover {
    color: #409eff;
  }

  .form-column-child {
    margin: 1px;
    flex-shrink: 0;
    border: 1px solid #e4e7ed !important;
    background: white;
    border-radius: 4px;
    transition: all 0.2s;
    padding: 8px;
  }

  .form-column-child:last-child {
    margin-bottom: 4px; /* 最后一个元素保持底部边距，避免压边框 */
  }

  /* 列内子组件悬停样式 */
  .form-column-child:hover:not(.form-child-preview) {
    border-color: #409eff !important;
    background-color: rgba(64, 158, 255, 0.02) !important;
  }

  /* 列内子组件选中样式 */
  .form-column-child.form-child-selected {
    border-color: #409eff !important;
    background-color: rgba(64, 158, 255, 0.05) !important;
  }

  /* 预览模式下的列内子组件样式 */
  .form-column-child.form-child-preview {
    border: none !important;
    background: transparent !important;
    padding: 0 !important;
  }

  /* 列容器中的表单控件宽度设置 */
  .form-column-child .el-input,
  .form-column-child .el-select,
  .form-column-child .el-date-editor,
  .form-column-child .el-input__wrapper,
  .form-column-child .el-picker,
  .form-column-child .el-picker__wrapper,
  .form-column-child .el-date-picker,
  .form-column-child .el-date-picker .el-input,
  .form-column-child .el-date-picker .el-input__wrapper,
  .form-column-child .el-range-editor,
  .form-column-child .el-range-input {
    width: 100% !important;
    box-sizing: border-box;
  }

  /* 列容器中的表单项宽度设置 */
  .form-column-child .form-child-form-item {
    width: 100%;
  }

  .form-column-child .el-form-item__content {
    width: 100% !important;
  }

  /* 确保所有时间控件在表单项中都能正确铺满 */
  .form-column-child .el-form-item__content .el-date-picker,
  .form-column-child .el-form-item__content .el-date-editor,
  .form-column-child .el-form-item__content .el-range-editor {
    width: 100% !important;
    max-width: 100% !important;
    min-width: 100% !important;
  }

  /* 更强的样式覆盖，确保Element Plus DatePicker组件宽度 */
  .form-column-child .el-date-editor.el-input,
  .form-column-child .el-date-editor.el-input .el-input__wrapper {
    width: 100% !important;
  }

  /* 针对不同类型的时间选择器 */
  .form-column-child .el-date-editor--date,
  .form-column-child .el-date-editor--datetime,
  .form-column-child .el-date-editor--daterange,
  .form-column-child .el-date-editor--datetimerange,
  .form-column-child .el-date-editor--monthrange,
  .form-column-child .el-date-editor--year,
  .form-column-child .el-date-editor--month,
  .form-column-child .el-date-editor--dates,
  .form-column-child .el-date-editor--week {
    width: 100% !important;
    max-width: 100% !important;
    min-width: 100% !important;
  }

  /* 预览模式下表单控件的特殊处理 */
  .form-column-child.form-child-preview .el-input,
  .form-column-child.form-child-preview .el-select,
  .form-column-child.form-child-preview .el-date-editor,
  .form-column-child.form-child-preview .el-input__wrapper,
  .form-column-child.form-child-preview .el-picker,
  .form-column-child.form-child-preview .el-picker__wrapper,
  .form-column-child.form-child-preview .el-date-picker,
  .form-column-child.form-child-preview .el-date-picker .el-input,
  .form-column-child.form-child-preview .el-date-picker .el-input__wrapper,
  .form-column-child.form-child-preview .el-range-editor,
  .form-column-child.form-child-preview .el-range-input {
    width: 100% !important;
    box-sizing: border-box;
  }

  .form-column-child.form-child-preview .el-form-item {
    margin-bottom: 0 !important;
    width: 100%;
  }

  .form-column-child.form-child-preview .el-form-item__content {
    width: 100% !important;
  }

  /* 预览模式下确保所有时间控件在表单项中都能正确铺满 */
  .form-column-child.form-child-preview .el-form-item__content .el-date-picker,
  .form-column-child.form-child-preview .el-form-item__content .el-date-editor,
  .form-column-child.form-child-preview
    .el-form-item__content
    .el-range-editor {
    width: 100% !important;
    max-width: 100% !important;
    min-width: 100% !important;
  }

  /* 预览模式下更强的样式覆盖 */
  .form-column-child.form-child-preview .el-date-editor.el-input,
  .form-column-child.form-child-preview
    .el-date-editor.el-input
    .el-input__wrapper {
    width: 100% !important;
  }

  /* 预览模式下针对不同类型的时间选择器 */
  .form-column-child.form-child-preview .el-date-editor--date,
  .form-column-child.form-child-preview .el-date-editor--datetime,
  .form-column-child.form-child-preview .el-date-editor--daterange,
  .form-column-child.form-child-preview .el-date-editor--datetimerange,
  .form-column-child.form-child-preview .el-date-editor--monthrange,
  .form-column-child.form-child-preview .el-date-editor--year,
  .form-column-child.form-child-preview .el-date-editor--month,
  .form-column-child.form-child-preview .el-date-editor--dates,
  .form-column-child.form-child-preview .el-date-editor--week {
    width: 100% !important;
    max-width: 100% !important;
    min-width: 100% !important;
  }

  /* 确保预览模式下列容器内边距正确 */
  .form-column-preview .form-column-inner {
    padding: 4px !important;
  }

  /* 表单列拖拽悬停状态 */
  .form-column-drag-over .form-column-inner {
    border-color: #409eff !important;
    border-style: solid !important;
    background-color: rgba(64, 158, 255, 0.05) !important;
  }

  /* 表单列选中状态 */
  .form-column-selected .form-column-inner {
    border: 2px solid #67c23a !important;
    border-style: solid !important;
    background-color: rgba(103, 194, 58, 0.05) !important;
    position: relative;
  }

  /* 表单列内子组件的操作按钮 */
  .form-column .form-child-actions {
    z-index: 12; /* 确保在列选中标签之下，但在其他元素之上 */
  }

  /* 按钮组内操作按钮的特殊样式 */
  .form-column-button-item .form-child-actions {
    position: absolute;
    top: -8px;
    right: -8px;
    z-index: 15; /* 确保在按钮之上 */
    flex: none !important; /* 防止被flex拉伸 */
    width: auto !important; /* 自适应宽度 */
    height: auto !important; /* 自适应高度 */
  }

  /* 操作按钮内的按钮元素不被拉伸 */
  .form-column-button-item .form-child-actions .el-button {
    min-width: auto !important;
    width: auto !important;
    height: auto !important;
    padding: 4px 6px !important;
    font-size: 12px !important;
  }

  /* 按钮组选中状态 */
  .form-column-button-group.selected {
    border-color: #67c23a !important;
    border-width: 2px !important;
  }

  /* 表单列按钮组样式 */
  .form-column-button-group {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    gap: 8px;
    margin: 1px; /* 与其他子元素保持一致的margin */
    margin-bottom: 8px; /* 与其他子元素保持一致的底部间距 */
    align-items: center;
    min-height: 48px; /* 增加最小高度，给操作按钮留足空间 */
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    background: white;
    box-sizing: border-box;
    position: relative; /* 为操作按钮定位提供参考 */
  }

  .form-column-button-item {
    margin: 0 !important;
    padding: 4px !important;
    border: none !important; /* 去掉单个按钮的边框 */
    border-radius: 4px;
    background: transparent;
    transition: all 0.2s;
    flex: none; /* 不自动拉伸 */
    min-width: 80px; /* 最小宽度 */
    max-width: 120px; /* 最大宽度 */
    min-height: 32px; /* 确保有足够高度容纳操作按钮 */
    position: relative; /* 为操作按钮提供定位参考 */
  }

  .form-column-button-item:hover:not(.form-child-preview) {
    background-color: rgba(64, 158, 255, 0.08) !important;
  }

  .form-column-button-item.form-child-selected {
    background-color: rgba(64, 158, 255, 0.15) !important;
    border: 2px solid #409eff !important;
    padding: 2px !important; /* 调整padding以补偿边框宽度增加 */
  }

  .form-column-button-item.form-child-preview {
    border: none !important;
    background: transparent !important;
    padding: 0 !important;
  }

  /* 预览模式下按钮组的样式 */
  .form-column-preview .form-column-button-group {
    border: none !important;
    background: transparent !important;
    padding: 0 !important;
    margin: 1px !important;
    margin-bottom: 8px !important;
    min-height: auto !important; /* 移除最小高度限制 */
  }

  .form-column-button-item .form-child-form-item {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0;
    height: 32px; /* 与其他表单控件的高度保持一致 */
  }

  .form-column-button-item .el-button {
    margin: 0;
    min-width: 72px; /* 按钮最小宽度 */
    max-width: 100%; /* 不超过容器 */
    width: auto; /* 自适应内容宽度 */
    flex: none; /* 取消flex拉伸 */
    white-space: nowrap; /* 文字不换行 */
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 14px;
    padding: 8px 12px;
  }

  /* 拖拽相关样式 */
  .form-child-dragging {
    opacity: 0.5;
    transform: scale(0.95);
    transition: all 0.2s ease;
    z-index: 1000;
    pointer-events: none;
  }

  .form-column-drag-over {
    background-color: #e8f4fd !important;
    border: 2px dashed #409eff !important;
    animation: dragPulse 1s infinite;
  }

  @keyframes dragPulse {
    0% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.02);
    }
    100% {
      transform: scale(1);
    }
  }

  .form-column-child[draggable='true'] {
    cursor: move;
  }

  .form-column-child[draggable='true']:hover {
    background-color: #f0f8ff !important;
    border-color: #409eff !important;
    box-shadow: 0 2px 4px rgba(64, 158, 255, 0.2);
  }

  .form-column-button-item[draggable='true'] {
    cursor: move;
    transition: all 0.2s ease;
  }

  .form-column-button-item[draggable='true']:hover {
    background-color: #f0f8ff !important;
    border-color: #409eff !important;
    box-shadow: 0 2px 4px rgba(64, 158, 255, 0.2);
  }

  /* 拖拽开始时的视觉提醒 */
  .form-column-child[draggable='true']:active,
  .form-column-button-item[draggable='true']:active {
    transform: scale(0.98);
    opacity: 0.8;
  }

  /* 调整位置模式样式 */
  .form-column-adjust-mode .form-column-inner {
    border: 2px dashed #f39c12 !important;
    background-color: rgba(243, 156, 18, 0.05) !important;
    cursor: pointer !important;
    transition: all 0.3s ease;
  }

  .form-column-adjust-mode .form-column-inner:hover {
    border-color: #e67e22 !important;
    background-color: rgba(243, 156, 18, 0.1) !important;
    transform: scale(1.02);
  }

  .form-column-adjust-mode::after {
    content: '点击将组件移动到此列';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: #f39c12;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    z-index: 10;
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
  }

  .form-column-adjust-mode:hover::after {
    opacity: 1;
  }

  /* 被选中用于调整的组件高亮样式 */
  .form-column-child.form-child-adjusting {
    border: 2px solid #e74c3c !important;
    background-color: rgba(231, 76, 60, 0.1) !important;
    animation: adjustPulse 1.5s infinite;
  }

  .form-column-button-item.form-child-adjusting {
    border: 2px solid #e74c3c !important;
    background-color: rgba(231, 76, 60, 0.1) !important;
    animation: adjustPulse 1.5s infinite;
  }

  @keyframes adjustPulse {
    0% {
      box-shadow: 0 0 0 0 rgba(231, 76, 60, 0.4);
    }
    50% {
      box-shadow: 0 0 0 8px rgba(231, 76, 60, 0.1);
    }
    100% {
      box-shadow: 0 0 0 0 rgba(231, 76, 60, 0);
    }
  }

  /* 编辑器占位符样式 */
  .editor-placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    min-height: 80px;
    background: rgba(64, 158, 255, 0.05);
    border: 1px dashed rgba(64, 158, 255, 0.3);
    border-radius: 4px;
    color: #606266;
    font-size: 14px;
  }

  .placeholder-content {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .empty-container-placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    min-height: 80px;
    background: rgba(64, 158, 255, 0.05);
    border: 1px dashed rgba(64, 158, 255, 0.3);
    border-radius: 4px;
    color: #606266;
    font-size: 14px;
  }

  /* 插槽子组件包装器样式 */
  .slot-child-wrapper {
    width: 100%;
    height: 100%;
    cursor: pointer;
    position: relative;
    border-radius: 4px;
    transition: all 0.2s ease;
  }

  .slot-child-wrapper:hover {
    box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.3);
  }

  .slot-child-wrapper.slot-child-selected {
    box-shadow: 0 0 0 2px #409eff;
    background-color: rgba(64, 158, 255, 0.05);
  }

  .slot-child-component {
    width: 100%;
    height: 100%;
    pointer-events: none; /* 防止内部组件的事件干扰 */
    border-radius: 4px;
  }

  /* 插槽子组件删除按钮 */
  .slot-child-delete-btn {
    position: absolute;
    top: -10px;
    right: -10px;
    width: 20px;
    height: 20px;
    background: #f56c6c;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: white;
    font-size: 12px;
    transition: all 0.2s;
    z-index: 1001;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  }

  .slot-child-delete-btn:hover {
    background: #f78989;
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
  }
</style>
