<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>响应式画布测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }
        .test-description {
            color: #666;
            margin-bottom: 15px;
        }
        .responsive-demo {
            width: 100vw;
            height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            font-weight: bold;
            position: fixed;
            top: 0;
            left: 0;
            z-index: 1000;
        }
        .fixed-demo {
            width: 1920px;
            height: 1080px;
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            font-weight: bold;
            margin: 10px 0;
            transform-origin: top left;
            transform: scale(0.3);
        }
        .demo-button {
            padding: 10px 20px;
            background: #409eff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .demo-button:hover {
            background: #66b1ff;
        }
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>响应式画布功能测试</h1>
        
        <div class="test-section">
            <div class="test-title">1. 响应式模式演示</div>
            <div class="test-description">
                点击按钮查看响应式模式效果（100vw × 100vh）
            </div>
            <button class="demo-button" onclick="showResponsiveDemo()">显示响应式演示</button>
            <button class="demo-button" onclick="hideResponsiveDemo()">隐藏演示</button>
        </div>

        <div class="test-section">
            <div class="test-title">2. 固定尺寸模式演示</div>
            <div class="test-description">
                固定尺寸模式（1920 × 1080px，缩放显示）
            </div>
            <div class="fixed-demo">
                固定尺寸模式<br>1920 × 1080px
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">3. 功能说明</div>
            <div class="test-description">
                <h4>响应式画布模式特点：</h4>
                <ul>
                    <li>编辑时仍然以1920×1080为基准进行设计</li>
                    <li>预览时画布使用100vw和100vh，自动适应屏幕尺寸</li>
                    <li>出码时生成的CSS使用100vw和100vh</li>
                    <li>画布右上角显示响应式模式提示</li>
                </ul>
                
                <h4>使用场景：</h4>
                <ul>
                    <li>需要适应不同屏幕尺寸的仪表板</li>
                    <li>移动端和桌面端自适应布局</li>
                    <li>全屏显示的数据大屏</li>
                </ul>
            </div>
        </div>
    </div>

    <div id="responsiveDemo" class="responsive-demo hidden">
        响应式模式演示<br>
        100vw × 100vh<br>
        <small>点击任意位置关闭</small>
    </div>

    <script>
        function showResponsiveDemo() {
            document.getElementById('responsiveDemo').classList.remove('hidden');
        }

        function hideResponsiveDemo() {
            document.getElementById('responsiveDemo').classList.add('hidden');
        }

        // 点击响应式演示区域关闭
        document.getElementById('responsiveDemo').addEventListener('click', hideResponsiveDemo);
    </script>
</body>
</html>
