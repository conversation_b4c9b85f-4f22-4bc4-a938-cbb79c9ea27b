<template>
  <!-- 圆环模式开关 -->
  <ElFormItem label="圆环模式">
    <ElSwitch :model-value="isDonutMode" @change="toggleDonutMode" />
  </ElFormItem>

  <!-- 普通饼图：只有外半径 -->
  <ElFormItem label="半径大小" v-if="!isDonutMode">
    <div class="radius-control">
      <ElSlider
        :model-value="outerRadius"
        @input="updateOuterRadius"
        :min="20"
        :max="90"
        :step="5"
        show-input
        :format-tooltip="formatTooltip"
      />
      <span class="radius-unit">%</span>
    </div>
  </ElFormItem>

  <!-- 圆环图：内半径和外半径 -->
  <template v-if="isDonutMode">
    <ElFormItem label="内半径">
      <div class="radius-control">
        <ElSlider
          :model-value="innerRadius"
          @input="updateInnerRadius"
          :min="0"
          :max="outerRadius - 10"
          :step="5"
          show-input
          :format-tooltip="formatTooltip"
        />
        <span class="radius-unit">%</span>
      </div>
    </ElFormItem>

    <ElFormItem label="外半径">
      <div class="radius-control">
        <ElSlider
          :model-value="outerRadius"
          @input="updateOuterRadius"
          :min="innerRadius + 10"
          :max="90"
          :step="5"
          show-input
          :format-tooltip="formatTooltip"
        />
        <span class="radius-unit">%</span>
      </div>
    </ElFormItem>
  </template>

  <!-- 位置调整 -->
  <ElFormItem label="水平位置">
    <div class="position-control">
      <ElSlider
        :model-value="centerX"
        @input="updateCenterX"
        :min="10"
        :max="90"
        :step="5"
        show-input
        :format-tooltip="formatTooltip"
      />
      <span class="position-unit">%</span>
    </div>
  </ElFormItem>

  <ElFormItem label="垂直位置">
    <div class="position-control">
      <ElSlider
        :model-value="centerY"
        @input="updateCenterY"
        :min="10"
        :max="90"
        :step="5"
        show-input
        :format-tooltip="formatTooltip"
      />
      <span class="position-unit">%</span>
    </div>
  </ElFormItem>
</template>

<script setup>
  import { computed } from 'vue'
  import { ElFormItem, ElSlider, ElSwitch } from 'element-plus'

  const props = defineProps({
    pieConfig: {
      type: Object,
      required: true
    }
  })

  const emit = defineEmits(['update-property'])

  // 判断是否为圆环模式
  const isDonutMode = computed(() => {
    return Array.isArray(props.pieConfig.radius)
  })

  // 获取内半径
  const innerRadius = computed(() => {
    if (Array.isArray(props.pieConfig.radius)) {
      return parseInt(props.pieConfig.radius[0]) || 0
    }
    return 0
  })

  // 获取外半径
  const outerRadius = computed(() => {
    if (Array.isArray(props.pieConfig.radius)) {
      return parseInt(props.pieConfig.radius[1]) || 75
    }
    return parseInt(props.pieConfig.radius) || 75
  })

  // 获取水平位置
  const centerX = computed(() => {
    if (Array.isArray(props.pieConfig.center)) {
      return parseInt(props.pieConfig.center[0]) || 50
    }
    return 50
  })

  // 获取垂直位置
  const centerY = computed(() => {
    if (Array.isArray(props.pieConfig.center)) {
      return parseInt(props.pieConfig.center[1]) || 50
    }
    return 50
  })

  // 切换圆环模式
  function toggleDonutMode(value) {
    if (value) {
      // 切换到圆环模式：[内半径, 外半径]
      const outer = outerRadius.value
      const inner = Math.max(0, outer - 30) // 默认内半径比外半径小30%
      emit('update-property', 'config.pieConfig.radius', [
        `${inner}%`,
        `${outer}%`
      ])
    } else {
      // 切换到普通模式：只有外半径
      emit(
        'update-property',
        'config.pieConfig.radius',
        `${outerRadius.value}%`
      )
    }
  }

  // 更新内半径
  function updateInnerRadius(value) {
    const newRadius = [`${value}%`, `${outerRadius.value}%`]
    emit('update-property', 'config.pieConfig.radius', newRadius)
  }

  // 更新外半径
  function updateOuterRadius(value) {
    if (isDonutMode.value) {
      const newRadius = [`${innerRadius.value}%`, `${value}%`]
      emit('update-property', 'config.pieConfig.radius', newRadius)
    } else {
      emit('update-property', 'config.pieConfig.radius', `${value}%`)
    }
  }

  // 更新水平位置
  function updateCenterX(value) {
    const newCenter = [`${value}%`, `${centerY.value}%`]
    emit('update-property', 'config.pieConfig.center', newCenter)
  }

  // 更新垂直位置
  function updateCenterY(value) {
    const newCenter = [`${centerX.value}%`, `${value}%`]
    emit('update-property', 'config.pieConfig.center', newCenter)
  }

  function formatTooltip(value) {
    return `${value}%`
  }
</script>

<style scoped>
  .radius-control {
    display: flex;
    align-items: center;
    gap: 8px;
    width: 100%;
  }

  .radius-control .el-slider {
    flex: 1;
  }

  .radius-unit,
  .position-unit {
    font-size: 12px;
    color: #606266;
    min-width: 20px;
  }

  .position-control {
    display: flex;
    align-items: center;
    gap: 8px;
    width: 100%;
  }

  .position-control .el-slider {
    flex: 1;
  }
</style>
