<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>边界限制和参考线功能测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: Arial, sans-serif;
            background: #f5f5f5;
            padding: 20px;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.1);
        }
        
        .test-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 20px;
            color: #333;
            text-align: center;
        }
        
        .feature-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e4e7ed;
            border-radius: 6px;
        }
        
        .feature-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #409eff;
        }
        
        .feature-description {
            color: #666;
            margin-bottom: 15px;
            line-height: 1.6;
        }
        
        .test-steps {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 15px;
        }
        
        .test-steps h4 {
            color: #333;
            margin-bottom: 10px;
        }
        
        .test-steps ol {
            padding-left: 20px;
        }
        
        .test-steps li {
            margin-bottom: 5px;
            color: #666;
        }
        
        .expected-behavior {
            background: #e8f5e8;
            padding: 15px;
            border-radius: 4px;
            border-left: 4px solid #67c23a;
        }
        
        .expected-behavior h4 {
            color: #67c23a;
            margin-bottom: 10px;
        }
        
        .expected-behavior ul {
            padding-left: 20px;
        }
        
        .expected-behavior li {
            margin-bottom: 5px;
            color: #333;
        }
        
        .demo-canvas {
            width: 100%;
            height: 400px;
            border: 2px solid #ddd;
            position: relative;
            background: 
                linear-gradient(90deg, rgba(0,0,0,0.1) 1px, transparent 1px),
                linear-gradient(rgba(0,0,0,0.1) 1px, transparent 1px);
            background-size: 20px 20px;
            margin: 20px 0;
            overflow: hidden;
        }
        
        .demo-component {
            position: absolute;
            background: #409eff;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
            cursor: move;
            user-select: none;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
        }
        
        .demo-component:hover {
            background: #66b1ff;
        }
        
        .demo-component.dragging {
            z-index: 1000;
            box-shadow: 0 4px 16px rgba(0,0,0,0.3);
        }
        
        .guide-line {
            position: absolute;
            pointer-events: none;
            z-index: 999;
        }
        
        .guide-line-vertical {
            width: 1px;
            height: 100%;
            top: 0;
            background-color: #f56c6c;
            box-shadow: 0 0 2px rgba(245, 108, 108, 0.5);
        }
        
        .guide-line-horizontal {
            width: 100%;
            height: 1px;
            left: 0;
            background-color: #f56c6c;
            box-shadow: 0 0 2px rgba(245, 108, 108, 0.5);
        }
        
        .status-indicator {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-weight: bold;
        }
        
        .status-good {
            background: #f0f9ff;
            color: #0369a1;
            border: 1px solid #7dd3fc;
        }
        
        .status-warning {
            background: #fffbeb;
            color: #d97706;
            border: 1px solid #fcd34d;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="test-title">🎯 边界限制和参考线功能测试</h1>
        
        <div class="feature-section">
            <div class="feature-title">1. 组件边界限制</div>
            <div class="feature-description">
                确保所有组件在拖拽和调整大小时不能超出画布边界。
            </div>
            
            <div class="test-steps">
                <h4>测试步骤：</h4>
                <ol>
                    <li>拖拽下方的蓝色组件到画布边缘</li>
                    <li>尝试将组件拖拽到画布外部</li>
                    <li>观察组件是否被限制在画布边界内</li>
                </ol>
            </div>
            
            <div class="expected-behavior">
                <h4>预期行为：</h4>
                <ul>
                    <li>✅ 组件无法被拖拽到画布左边界外</li>
                    <li>✅ 组件无法被拖拽到画布右边界外</li>
                    <li>✅ 组件无法被拖拽到画布上边界外</li>
                    <li>✅ 组件无法被拖拽到画布下边界外</li>
                </ul>
            </div>
        </div>

        <div class="feature-section">
            <div class="feature-title">2. 拖拽参考线</div>
            <div class="feature-description">
                拖拽组件时显示参考线，帮助对齐其他组件和画布边界。
            </div>
            
            <div class="test-steps">
                <h4>测试步骤：</h4>
                <ol>
                    <li>开始拖拽任意组件</li>
                    <li>观察是否显示红色参考线</li>
                    <li>将组件靠近其他组件或画布边界</li>
                    <li>观察组件是否自动吸附到参考线</li>
                </ol>
            </div>
            
            <div class="expected-behavior">
                <h4>预期行为：</h4>
                <ul>
                    <li>✅ 拖拽时显示垂直和水平参考线</li>
                    <li>✅ 参考线包括画布边界和中心线</li>
                    <li>✅ 参考线包括其他组件的边界和中心线</li>
                    <li>✅ 组件在接近参考线时自动吸附</li>
                    <li>✅ 停止拖拽时参考线消失</li>
                </ul>
            </div>
        </div>

        <!-- 演示画布 -->
        <div class="demo-canvas" id="demoCanvas">
            <div class="demo-component" style="left: 50px; top: 50px; width: 100px; height: 60px;">
                组件 A
            </div>
            <div class="demo-component" style="left: 200px; top: 150px; width: 120px; height: 80px;">
                组件 B
            </div>
            <div class="demo-component" style="left: 350px; top: 80px; width: 80px; height: 100px;">
                组件 C
            </div>
        </div>

        <div class="status-indicator status-good">
            💡 提示：这是一个简化的演示。实际的边界限制和参考线功能已在 EditorCanvas.vue 中实现。
        </div>

        <div class="feature-section">
            <div class="feature-title">3. 实现细节</div>
            <div class="feature-description">
                <h4>边界限制实现：</h4>
                <ul>
                    <li>在 handleDragMove 中添加画布边界检测</li>
                    <li>在 handleDragEnd 中确保最终位置在边界内</li>
                    <li>在 calculateNewDimensions 中限制调整大小时的边界</li>
                    <li>支持响应式画布和固定尺寸画布</li>
                </ul>
                
                <h4>参考线实现：</h4>
                <ul>
                    <li>calculateGuidePositions 计算所有可能的参考线位置</li>
                    <li>包括画布边界、中心线和其他组件的边界</li>
                    <li>拖拽时实时显示参考线</li>
                    <li>5px 吸附阈值，支持左对齐、右对齐、中心对齐</li>
                    <li>坐标转换确保在不同缩放比例下正确显示</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        // 简单的拖拽演示（仅用于展示概念）
        let isDragging = false;
        let currentElement = null;
        let startPos = { x: 0, y: 0 };
        let elementStartPos = { x: 0, y: 0 };

        document.querySelectorAll('.demo-component').forEach(element => {
            element.addEventListener('mousedown', (e) => {
                isDragging = true;
                currentElement = element;
                startPos = { x: e.clientX, y: e.clientY };
                elementStartPos = {
                    x: parseInt(element.style.left),
                    y: parseInt(element.style.top)
                };
                element.classList.add('dragging');
                e.preventDefault();
            });
        });

        document.addEventListener('mousemove', (e) => {
            if (!isDragging || !currentElement) return;

            const deltaX = e.clientX - startPos.x;
            const deltaY = e.clientY - startPos.y;
            
            let newX = elementStartPos.x + deltaX;
            let newY = elementStartPos.y + deltaY;

            // 简单的边界限制演示
            const canvas = document.getElementById('demoCanvas');
            const canvasRect = canvas.getBoundingClientRect();
            const elementRect = currentElement.getBoundingClientRect();
            
            newX = Math.max(0, Math.min(newX, canvas.offsetWidth - currentElement.offsetWidth));
            newY = Math.max(0, Math.min(newY, canvas.offsetHeight - currentElement.offsetHeight));

            currentElement.style.left = newX + 'px';
            currentElement.style.top = newY + 'px';
        });

        document.addEventListener('mouseup', () => {
            if (currentElement) {
                currentElement.classList.remove('dragging');
            }
            isDragging = false;
            currentElement = null;
        });
    </script>
</body>
</html>
