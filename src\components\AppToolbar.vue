<template>
  <div class="toolbar-container">
    <div class="toolbar-left">
      <h2 class="title">BI 可视化布局系统</h2>
    </div>

    <div class="toolbar-center">
      <!-- 画布尺寸选择 -->
      <ElSelect
        v-model="selectedSize"
        placeholder="选择画布尺寸"
        @change="handleSizeChange"
        style="width: 180px"
      >
        <ElOptionGroup label="常用尺寸">
          <ElOption
            v-for="item in commonSizes"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </ElOptionGroup>
        <ElOptionGroup label="其他">
          <ElOption label="自定义" value="custom" />
        </ElOptionGroup>
      </ElSelect>

      <!-- 自定义尺寸输入 -->
      <div v-if="selectedSize === 'custom'" class="size-input-group">
        <span class="unit-label">宽度 (px)</span>
        <ElInputNumber
          v-model="canvasWidth"
          :min="300"
          :max="5000"
          controls-position="right"
          @change="handleCustomChange"
          class="size-input"
        />
        <span class="separator">x</span>
        <span class="unit-label">高度 (px)</span>
        <ElInputNumber
          v-model="canvasHeight"
          :min="300"
          :max="5000"
          controls-position="right"
          @change="handleCustomChange"
          class="size-input"
        />
      </div>

      <!-- 分隔符 -->
      <div class="toolbar-separator"></div>

      <!-- 缩放控制 -->
      <ScaleControls :show-info="true" />
    </div>

    <div class="toolbar-right">
      <!-- 清空画布按钮 - 纯图标 -->
      <ElButton
        type="danger"
        :icon="Delete"
        @click="handleClearCanvas"
        size="small"
        class="icon-button pure-icon-button"
        title="清空画布"
      />

      <!-- 预览按钮 - 纯图标 -->
      <ElButton
        type="primary"
        :icon="View"
        @click="$emit('preview')"
        size="small"
        class="icon-button pure-icon-button"
        title="预览"
      />

      <ElButton
        type="success"
        :icon="Download"
        @click="$emit('save')"
        size="small"
      >
        保存
      </ElButton>

      <ElButton
        type="warning"
        :icon="Download"
        @click="showCodeGenerateDialog = true"
        size="small"
      >
        出码
      </ElButton>

      <!-- 省略号下拉菜单 -->
      <ElDropdown @command="handleMenuCommand">
        <ElButton
          :icon="More"
          size="small"
          class="icon-button pure-icon-button"
          title="更多"
        />
        <template #dropdown>
          <ElDropdownMenu>
            <ElDropdownItem command="schema">
              <ElIcon><Document /></ElIcon>
              查看Schema
            </ElDropdownItem>
          </ElDropdownMenu>
        </template>
      </ElDropdown>
    </div>

    <!-- Schema JSON 对话框 -->
    <ElDialog
      v-model="showSchemaDialog"
      title="Schema JSON"
      width="800px"
      destroy-on-close
    >
      <div class="schema-content">
        <pre>{{ schemaJson }}</pre>
      </div>
      <template #footer>
        <ElButton @click="showSchemaDialog = false">关闭</ElButton>
        <ElButton type="primary" @click="copySchemaToClipboard">
          复制到剪贴板
        </ElButton>
      </template>
    </ElDialog>

    <!-- 代码生成对话框 -->
    <ElDialog
      v-model="showCodeGenerateDialog"
      title="代码生成"
      width="520px"
      destroy-on-close
      :show-close="false"
      class="code-generate-dialog"
    >
      <div class="code-generate-content">
        <div class="dialog-header">
          <div class="header-icon">
            <svg width="32" height="32" viewBox="0 0 24 24" fill="none">
              <path
                d="M9 12l2 2 4-4"
                stroke="#52c41a"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
              <circle
                cx="12"
                cy="12"
                r="10"
                stroke="#52c41a"
                stroke-width="2"
              />
            </svg>
          </div>
          <h3 class="dialog-title">选择目标框架版本</h3>
          <p class="dialog-subtitle">选择您希望生成的Vue代码版本</p>
        </div>

        <div class="code-type-options">
          <div class="radio-group">
            <div
              class="radio-option"
              :class="{ 'is-checked': selectedCodeType === 'vue2' }"
              @click="selectedCodeType = 'vue2'"
            >
              <div class="radio-header">
                <ElRadio
                  v-model="selectedCodeType"
                  label="vue2"
                  class="custom-radio"
                >
                </ElRadio>
              </div>
              <div class="radio-content">
                <div class="version-info">
                  <div class="version-badge vue2-badge">Vue 2</div>
                  <div class="tech-stack">Element UI</div>
                </div>
                <div class="radio-desc">
                  适用于传统项目，成熟稳定的Options API语法
                </div>
                <div class="features">
                  <span class="feature-tag">Options API</span>
                  <span class="feature-tag">Element UI 2.x</span>
                </div>
              </div>
            </div>

            <div
              class="radio-option"
              :class="{ 'is-checked': selectedCodeType === 'vue3' }"
              @click="selectedCodeType = 'vue3'"
            >
              <div class="radio-header">
                <ElRadio
                  v-model="selectedCodeType"
                  label="vue3"
                  class="custom-radio"
                >
                </ElRadio>
              </div>
              <div class="radio-content">
                <div class="version-info">
                  <div class="version-badge vue3-badge">Vue 3</div>
                  <div class="tech-stack">Element Plus</div>
                </div>
                <div class="radio-desc">
                  现代化开发，更好的性能和TypeScript支持
                </div>
                <div class="features">
                  <span class="feature-tag">Composition API</span>
                  <span class="feature-tag">Element Plus</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <ElButton @click="showCodeGenerateDialog = false" size="large">
            取消
          </ElButton>
          <ElButton
            type="primary"
            @click="generateCode"
            :loading="isGeneratingCode"
            size="large"
          >
            {{ isGeneratingCode ? '生成中...' : '生成代码' }}
          </ElButton>
        </div>
      </template>
    </ElDialog>

    <!-- 代码生成成功对话框 -->
    <ElDialog
      v-model="showCodeSuccessDialog"
      title=""
      width="600px"
      destroy-on-close
      :show-close="false"
      class="code-success-dialog"
    >
      <div class="code-success-content">
        <div class="success-header">
          <div class="success-icon">
            <svg width="48" height="48" viewBox="0 0 24 24" fill="none">
              <path
                d="M9 12l2 2 4-4"
                stroke="#52c41a"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
              <circle
                cx="12"
                cy="12"
                r="10"
                stroke="#52c41a"
                stroke-width="2"
              />
            </svg>
          </div>
          <h3 class="success-title">代码生成成功！</h3>
        </div>

        <div class="action-buttons">
          <ElButton
            type="primary"
            :icon="Download"
            @click="downloadGeneratedCode"
            size="large"
          >
            下载文件
          </ElButton>
          <ElButton :icon="View" @click="previewGeneratedCode" size="large">
            预览代码
          </ElButton>
          <ElButton :icon="Document" @click="copyGeneratedCode" size="large">
            复制代码
          </ElButton>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <ElButton @click="showCodeSuccessDialog = false" size="large">
            取消
          </ElButton>
          <ElButton type="primary" @click="generateNewCode" size="large">
            生成代码
          </ElButton>
        </div>
      </template>
    </ElDialog>

    <!-- 代码预览对话框 -->
    <ElDialog
      v-model="showCodePreviewDialog"
      title="代码预览"
      width="80%"
      destroy-on-close
      class="code-preview-dialog"
    >
      <div class="code-preview-content">
        <div class="preview-header">
          <span class="file-name">{{ previewFileName }}</span>
          <ElButton
            type="primary"
            size="small"
            @click="copyPreviewCode"
            :icon="Document"
          >
            复制代码
          </ElButton>
        </div>
        <pre class="code-preview"><code>{{ previewCode }}</code></pre>
      </div>
      <template #footer>
        <ElButton @click="showCodePreviewDialog = false">关闭</ElButton>
      </template>
    </ElDialog>
  </div>
</template>

<script setup>
  import { ref, watch } from 'vue'
  import {
    ElButton,
    ElSelect,
    ElOption,
    ElInputNumber,
    ElOptionGroup,
    ElDialog,
    ElMessage,
    ElRadio,
    ElMessageBox,
    ElDropdown,
    ElDropdownMenu,
    ElDropdownItem,
    ElIcon
  } from 'element-plus'
  import {
    View,
    Download,
    Document,
    Delete,
    More
  } from '@element-plus/icons-vue'
  import ScaleControls from './ScaleControls.vue'
  import { useEditorStore } from '../store/editor'

  // 定义事件
  const emit = defineEmits(['preview', 'save', 'clear', 'update:size'])

  // 接收画布组件列表作为属性
  const props = defineProps({
    canvasComponents: {
      type: Array,
      required: true
    }
  })

  // 使用 editor store
  const editorStore = useEditorStore()

  // Schema 对话框相关
  const showSchemaDialog = ref(false)
  const schemaJson = ref('')

  // 代码生成对话框相关
  const showCodeGenerateDialog = ref(false)
  const selectedCodeType = ref('vue3')
  const isGeneratingCode = ref(false)

  // 代码生成成功对话框相关
  const showCodeSuccessDialog = ref(false)
  const generatedCode = ref('')
  const generatedFileName = ref('')

  // 代码预览对话框相关
  const showCodePreviewDialog = ref(false)
  const previewCode = ref('')
  const previewFileName = ref('')

  // 生成层级化schema的方法
  const generateSchemaJson = async () => {
    // 安全检查，确保 canvasComponents 存在
    if (!props.canvasComponents || !Array.isArray(props.canvasComponents)) {
      const schema = {
        canvas: {
          width: canvasWidth.value,
          height: canvasHeight.value
        },
        version: '2.0',
        variables: editorStore.schema.variables || {},
        methods: editorStore.schema.methods || '',
        mounted: editorStore.schema.mounted || '',
        styles: editorStore.schema.styles || '',
        components: []
      }
      return JSON.stringify(schema, null, 2)
    }

    // 动态导入schema转换工具
    const { generateHierarchicalSchema, cleanComponentConfig } = await import(
      '../utils/schemaTransform.js'
    )

    const canvasSize = {
      width: canvasWidth.value,
      height: canvasHeight.value
    }

    // 生成层级化的schema
    const hierarchicalSchema = generateHierarchicalSchema(
      canvasSize,
      props.canvasComponents
    )

    // 清理配置并创建最终的schema，包含store中的样式等信息
    const finalSchema = {
      ...hierarchicalSchema,
      variables: editorStore.schema.variables || {},
      methods: editorStore.schema.methods || '',
      mounted: editorStore.schema.mounted || '',
      styles: editorStore.schema.styles || '',
      components: hierarchicalSchema.components.map(cleanComponentConfig)
    }

    return JSON.stringify(finalSchema, null, 2)
  }

  // 监听schemaDialog显示状态，生成schema
  watch(showSchemaDialog, async newVal => {
    if (newVal) {
      schemaJson.value = await generateSchemaJson()
    }
  })

  // 复制到剪贴板
  const copySchemaToClipboard = async () => {
    try {
      await window.navigator.clipboard.writeText(schemaJson.value)
      ElMessage.success('已复制到剪贴板')
    } catch {
      ElMessage.error('复制失败')
    }
  }

  // 生成代码
  const generateCode = async () => {
    try {
      isGeneratingCode.value = true

      // 生成schema
      const schema = await generateSchemaData()

      // 调试信息：打印schema数据
      console.log('=== 生成的Schema数据 ===')
      console.log('Canvas Components:', props.canvasComponents)
      console.log('Generated Schema:', schema)
      console.log('========================')

      // 动态导入代码生成工具
      const { generateCode: generateCodeFromSchema } = await import(
        '../utils/generate-code/index.js'
      )

      // 生成代码
      const codeResult = generateCodeFromSchema(schema, selectedCodeType.value)

      // 调试信息：打印生成的代码
      console.log('=== 生成的代码 ===')
      console.log(codeResult)
      console.log('================')

      // 保存生成的代码
      generatedCode.value = codeResult
      generatedFileName.value = `generated-${selectedCodeType.value}-component.vue`

      // 显示成功对话框
      showCodeGenerateDialog.value = false
      showCodeSuccessDialog.value = true

      ElMessage.success(
        `${selectedCodeType.value.toUpperCase()} 代码生成成功！`
      )
    } catch (error) {
      console.error('代码生成失败:', error)
      ElMessage.error('代码生成失败: ' + error.message)
    } finally {
      isGeneratingCode.value = false
    }
  }

  // 下载生成的代码
  const downloadGeneratedCode = async () => {
    try {
      const { downloadCode } = await import('../utils/generate-code/index.js')
      downloadCode(generatedCode.value, generatedFileName.value)
      ElMessage.success('代码文件下载成功！')
    } catch (error) {
      console.error('下载失败:', error)
      ElMessage.error('下载失败: ' + error.message)
    }
  }

  // 预览代码
  const previewGeneratedCode = () => {
    previewCode.value = generatedCode.value
    previewFileName.value = generatedFileName.value
    showCodePreviewDialog.value = true
  }

  // 复制生成的代码
  const copyGeneratedCode = async () => {
    try {
      await window.navigator.clipboard.writeText(generatedCode.value)
      ElMessage.success('代码已复制到剪贴板')
    } catch (error) {
      console.error('复制失败:', error)
      ElMessage.error('复制失败')
    }
  }

  // 复制预览代码
  const copyPreviewCode = async () => {
    try {
      await window.navigator.clipboard.writeText(previewCode.value)
      ElMessage.success('代码已复制到剪贴板')
    } catch (error) {
      console.error('复制失败:', error)
      ElMessage.error('复制失败')
    }
  }

  // 生成新代码 (在成功对话框中)
  const generateNewCode = () => {
    showCodeSuccessDialog.value = false
    showCodeGenerateDialog.value = true
  }

  // 生成schema数据
  const generateSchemaData = async () => {
    // 安全检查，确保 canvasComponents 存在
    if (!props.canvasComponents || !Array.isArray(props.canvasComponents)) {
      return {
        canvas: {
          width: canvasWidth.value,
          height: canvasHeight.value
        },
        version: '2.0',
        variables: editorStore.schema.variables || {},
        methods: editorStore.schema.methods || '',
        mounted: editorStore.schema.mounted || '',
        styles: editorStore.schema.styles || '',
        components: []
      }
    }

    // 动态导入schema转换工具
    const { generateHierarchicalSchema, cleanComponentConfig } = await import(
      '../utils/schemaTransform.js'
    )

    const canvasSize = {
      width: canvasWidth.value,
      height: canvasHeight.value
    }

    // 生成层级化的schema
    const hierarchicalSchema = generateHierarchicalSchema(
      canvasSize,
      props.canvasComponents
    )

    // 清理配置并创建最终的schema，包含store中的样式等信息
    return {
      ...hierarchicalSchema,
      variables: editorStore.schema.variables || {},
      methods: editorStore.schema.methods || '',
      mounted: editorStore.schema.mounted || '',
      styles: editorStore.schema.styles || '',
      components: hierarchicalSchema.components.map(cleanComponentConfig)
    }
  }

  // 常用尺寸配置
  const commonSizes = [
    { label: '1920 x 1080 (16:9)', value: '1920x1080' },
    { label: '1366 x 768 (16:9)', value: '1366x768' },
    { label: '1280 x 720 (16:9)', value: '1280x720' },
    { label: '1024 x 768 (4:3)', value: '1024x768' },
    { label: '800 x 600 (4:3)', value: '800x600' }
  ]

  const selectedSize = ref('1920x1080')
  const canvasWidth = ref(1920)
  const canvasHeight = ref(1080)

  // 处理尺寸选择变化
  const handleSizeChange = value => {
    if (value === 'custom') {
      // 切换到自定义模式时保持当前尺寸
      return
    }

    // 解析选定的尺寸
    const [width, height] = value.split('x').map(Number)
    canvasWidth.value = width
    canvasHeight.value = height
    emitSizeUpdate()
  }

  // 处理自定义尺寸变化
  const handleCustomChange = () => {
    emitSizeUpdate()
  }

  // 发出尺寸更新事件
  const emitSizeUpdate = () => {
    emit('update:size', {
      width: canvasWidth.value,
      height: canvasHeight.value
    })
  }

  // 初始化时发出尺寸
  emitSizeUpdate()

  // 处理清空画布
  const handleClearCanvas = async () => {
    try {
      await ElMessageBox.confirm(
        '确定要清空画布吗？此操作不可恢复。',
        '确认清空',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )
      emit('clear')
    } catch {
      // 用户取消操作
    }
  }

  // 处理菜单命令
  const handleMenuCommand = command => {
    if (command === 'schema') {
      showSchemaDialog.value = true
    }
  }
</script>

<style scoped>
  .toolbar-container {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    border-bottom: 1px solid #e4e7ed;
  }

  .toolbar-left .title {
    margin: 0;
    font-size: 18px;
    font-weight: 500;
    color: #303133;
  }

  .toolbar-center {
    display: flex;
    align-items: center;
    gap: 16px;
  }

  .size-input-group {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .size-input {
    width: 80px;
  }

  .unit-label {
    font-size: 12px;
    color: #606266;
    white-space: nowrap;
  }

  .separator {
    color: #909399;
    font-weight: bold;
  }

  .toolbar-right {
    display: flex;
    gap: 10px;
    align-items: center;
  }

  .toolbar-right .el-button {
    padding: 6px 12px;
    font-size: 13px;
  }

  /* 纯图标按钮样式 */
  .toolbar-right .icon-button {
    padding: 6px 8px;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .toolbar-right .icon-button .el-icon {
    font-size: 16px;
  }

  /* 纯图标按钮样式 - 无背景色 */
  .toolbar-right .pure-icon-button {
    background: transparent !important;
    border: none !important;
    color: #606266 !important;
    box-shadow: none !important;
    padding: 6px 8px;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    transition: all 0.3s ease;
  }

  .toolbar-right .pure-icon-button:hover {
    background: rgba(0, 0, 0, 0.04) !important;
    color: #409eff !important;
  }

  .toolbar-right .pure-icon-button:active {
    background: rgba(0, 0, 0, 0.08) !important;
  }

  .toolbar-right .pure-icon-button.is-loading,
  .toolbar-right .pure-icon-button:focus {
    background: transparent !important;
    border: none !important;
    box-shadow: none !important;
  }

  /* 下拉菜单样式 */
  :deep(.el-dropdown) {
    height: 24px;
  }

  :deep(.el-dropdown-menu) {
    padding: 4px 0;
    min-width: 120px;
  }

  :deep(.el-dropdown-menu__item) {
    padding: 8px 12px;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 13px;
  }

  :deep(.el-dropdown-menu__item) .el-icon {
    font-size: 14px;
  }

  .schema-content {
    max-height: 600px;
    overflow-y: auto;
    background-color: #f8f9fa;
    padding: 16px;
    border-radius: 4px;
  }

  .schema-content pre {
    margin: 0;
    white-space: pre-wrap;
    word-wrap: break-word;
    font-family: Monaco, Menlo, Consolas, 'Courier New', monospace;
    font-size: 14px;
    line-height: 1.5;
  }

  /* 代码生成对话框样式 */
  :deep(.code-generate-dialog) .el-dialog__header {
    padding: 24px 24px 0;
    text-align: center;
  }

  :deep(.code-generate-dialog) .el-dialog__title {
    font-size: 18px;
    font-weight: 600;
    color: #303133;
  }

  :deep(.code-generate-dialog) .el-dialog__body {
    padding: 0 24px 24px;
  }

  .code-generate-content {
    padding: 0;
  }

  .dialog-header {
    text-align: center;
    margin-bottom: 32px;
  }

  .header-icon {
    margin-bottom: 16px;
    display: flex;
    justify-content: center;
  }

  .dialog-title {
    margin: 0 0 8px 0;
    font-size: 20px;
    font-weight: 600;
    color: #303133;
  }

  .dialog-subtitle {
    margin: 0;
    font-size: 14px;
    color: #909399;
  }

  .code-type-options {
    display: flex;
    justify-content: center;
  }

  .radio-group {
    display: flex;
    flex-direction: column;
    gap: 20px;
    width: 100%;
    max-width: 420px;
  }

  .radio-option {
    border: 2px solid #e4e7ed;
    border-radius: 12px;
    padding: 24px;
    margin: 0;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    position: relative;
    overflow: hidden;
    background: #fff;
  }

  .radio-option::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #409eff, #73d13d);
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .radio-option:hover {
    border-color: #409eff;
    background-color: #fafbfc;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(64, 158, 255, 0.1);
  }

  .radio-option.is-checked {
    border-color: #409eff;
    background-color: #f0f9ff;
    box-shadow: 0 4px 20px rgba(64, 158, 255, 0.15);
  }

  .radio-option.is-checked::before {
    opacity: 1;
  }

  .radio-header {
    position: absolute;
    top: 20px;
    right: 20px;
  }

  .custom-radio {
    margin: 0;
    pointer-events: none;
  }

  :deep(.custom-radio) .el-radio__label {
    display: none;
  }

  .radio-content {
    width: 100%;
    padding-right: 40px;
  }

  .version-info {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 12px;
  }

  .version-badge {
    padding: 6px 16px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 600;
    color: white;
  }

  .vue2-badge {
    background: linear-gradient(135deg, #ff6b6b, #ee5a52);
  }

  .vue3-badge {
    background: linear-gradient(135deg, #51cf66, #40c057);
  }

  .tech-stack {
    font-size: 13px;
    color: #909399;
    background: #f1f3f4;
    padding: 4px 10px;
    border-radius: 12px;
  }

  .radio-desc {
    font-size: 14px;
    color: #606266;
    line-height: 1.5;
    margin-bottom: 16px;
  }

  .features {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
  }

  .feature-tag {
    font-size: 12px;
    color: #409eff;
    background: rgba(64, 158, 255, 0.1);
    padding: 4px 10px;
    border-radius: 14px;
    border: 1px solid rgba(64, 158, 255, 0.2);
  }

  .dialog-footer {
    display: flex;
    justify-content: center;
    gap: 16px;
    padding: 24px 0 0;
    border-top: 1px solid #f0f0f0;
  }

  :deep(.code-generate-dialog) .el-dialog__footer {
    padding: 0 24px 24px;
  }

  /* 代码生成成功对话框样式 */
  :deep(.code-success-dialog) .el-dialog__header {
    padding: 24px 24px 0;
    text-align: center;
  }

  :deep(.code-success-dialog) .el-dialog__body {
    padding: 0 24px 24px;
  }

  .code-success-content {
    text-align: center;
    padding: 20px 0;
  }

  .success-header {
    margin-bottom: 40px;
  }

  .success-icon {
    margin-bottom: 16px;
    display: flex;
    justify-content: center;
  }

  .success-title {
    margin: 0;
    font-size: 22px;
    font-weight: 600;
    color: #303133;
  }

  .action-buttons {
    display: flex;
    justify-content: center;
    gap: 16px;
    flex-wrap: wrap;
  }

  .action-buttons .el-button {
    min-width: 120px;
    height: 44px;
    font-size: 14px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
  }

  :deep(.code-success-dialog) .el-dialog__footer {
    padding: 0 24px 24px;
  }

  /* 代码预览对话框样式 */
  :deep(.code-preview-dialog) .el-dialog__header {
    padding: 16px 24px;
    border-bottom: 1px solid #e4e7ed;
  }

  :deep(.code-preview-dialog) .el-dialog__body {
    padding: 0;
  }

  .code-preview-content {
    display: flex;
    flex-direction: column;
    height: 70vh;
  }

  .preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 24px;
    background: #f8f9fa;
    border-bottom: 1px solid #e4e7ed;
  }

  .file-name {
    font-family: Monaco, Menlo, Consolas, 'Courier New', monospace;
    font-size: 14px;
    color: #303133;
    font-weight: 600;
  }

  .code-preview {
    flex: 1;
    margin: 0;
    padding: 24px;
    background: #f8f9fa;
    overflow: auto;
    font-family: Monaco, Menlo, Consolas, 'Courier New', monospace;
    font-size: 14px;
    line-height: 1.6;
    color: #303133;
    white-space: pre-wrap;
    word-wrap: break-word;
  }

  .code-preview code {
    background: transparent;
    padding: 0;
    font-family: inherit;
    font-size: inherit;
    color: inherit;
  }

  :deep(.code-preview-dialog) .el-dialog__footer {
    padding: 16px 24px;
    border-top: 1px solid #e4e7ed;
    text-align: right;
  }

  /* 分隔符样式 */
  .toolbar-separator {
    width: 1px;
    height: 24px;
    background-color: #e4e7ed;
    margin: 0 16px;
  }

  /* 响应式调整 */
  @media (max-width: 1200px) {
    .toolbar-center {
      flex-wrap: wrap;
      gap: 12px;
    }
  }

  @media (max-width: 768px) {
    .toolbar-separator {
      display: none;
    }
  }
</style>
