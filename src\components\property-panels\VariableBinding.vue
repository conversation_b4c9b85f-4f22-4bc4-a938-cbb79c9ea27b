<template>
  <div class="variable-binding">
    <ElFormItem label="数据绑定">
      <ElSwitch
        :model-value="!!localConfig.dataBinding?.enabled"
        @change="handleBindingToggle"
        active-text="绑定变量"
        inactive-text="使用示例数据"
      />
    </ElFormItem>

    <!-- 变量绑定配置 -->
    <template v-if="localConfig.dataBinding?.enabled">
      <ElFormItem label="绑定变量">
        <ElSelect
          :model-value="localConfig.dataBinding?.variable"
          @change="handleVariableChange"
          size="small"
          placeholder="选择变量"
          filterable
          clearable
        >
          <ElOption
            v-for="(value, key) in availableVariables"
            :key="key"
            :label="key"
            :value="key"
          >
            <div class="variable-option">
              <span class="variable-name">{{ key }}</span>
              <span class="variable-type">{{ getVariableTypeLabel(value) }}</span>
            </div>
          </ElOption>
        </ElSelect>
      </ElFormItem>

      <!-- 数据映射配置 -->
      <template v-if="localConfig.dataBinding?.variable">
        <ElFormItem label="数据映射">
          <div class="mapping-config">
            <!-- 快速配置按钮 -->
            <div class="quick-config">
              <ElButton
                size="small"
                type="primary"
                @click="autoDetectMapping"
                :disabled="!selectedVariableData"
              >
                自动检测字段
              </ElButton>
              <ElText type="info" size="small">
                根据变量数据自动推荐字段映射
              </ElText>
            </div>

            <!-- X轴数据字段 -->
            <div class="mapping-item">
              <label>X轴字段:</label>
              <ElSelect
                :model-value="localConfig.dataBinding?.mapping?.xField || ''"
                @change="handleMappingChange('xField', $event)"
                size="small"
                placeholder="选择X轴字段"
                filterable
                allow-create
              >
                <ElOption
                  v-for="field in availableFields"
                  :key="field"
                  :label="field"
                  :value="field"
                />
              </ElSelect>
            </div>

            <!-- Y轴数据字段 -->
            <div class="mapping-item">
              <label>Y轴字段:</label>
              <ElSelect
                :model-value="localConfig.dataBinding?.mapping?.yField || ''"
                @change="handleMappingChange('yField', $event)"
                size="small"
                placeholder="选择Y轴字段"
                filterable
                allow-create
              >
                <ElOption
                  v-for="field in availableFields"
                  :key="field"
                  :label="field"
                  :value="field"
                />
              </ElSelect>
            </div>

            <!-- 系列字段（可选） -->
            <div class="mapping-item">
              <label>系列字段:</label>
              <ElSelect
                :model-value="localConfig.dataBinding?.mapping?.seriesField || ''"
                @change="handleMappingChange('seriesField', $event)"
                size="small"
                placeholder="选择系列字段（可选）"
                filterable
                allow-create
                clearable
              >
                <ElOption
                  v-for="field in availableFields"
                  :key="field"
                  :label="field"
                  :value="field"
                />
              </ElSelect>
            </div>
          </div>
        </ElFormItem>

        <!-- 预览当前变量数据 -->
        <ElFormItem label="数据预览">
          <div class="data-preview">
            <ElButton
              type="primary"
              size="small"
              @click="showDataPreview = !showDataPreview"
            >
              {{ showDataPreview ? '隐藏' : '查看' }}数据
            </ElButton>
            <div v-if="showDataPreview" class="preview-content">
              <pre>{{ formatVariableData }}</pre>
            </div>
          </div>
        </ElFormItem>
      </template>
    </template>

    <!-- 示例数据说明 -->
    <template v-else>
      <ElFormItem label="数据说明">
        <div class="data-info">
          <ElText type="info" size="small">
            当前使用示例数据。启用变量绑定后，图表将从指定变量获取数据。
          </ElText>
        </div>
      </ElFormItem>
    </template>
  </div>
</template>

<script setup>
  import { computed, ref } from 'vue'
  import {
    ElFormItem,
    ElSwitch,
    ElSelect,
    ElOption,
    ElInput,
    ElButton,
    ElText
  } from 'element-plus'

  const props = defineProps({
    config: {
      type: Object,
      required: true
    },
    variables: {
      type: Object,
      default: () => ({})
    }
  })

  const emit = defineEmits(['update-property'])

  const showDataPreview = ref(false)

  const localConfig = computed(() => props.config)

  // 获取可用的变量（过滤出数组和对象类型）
  const availableVariables = computed(() => {
    const filtered = {}
    Object.entries(props.variables).forEach(([key, value]) => {
      if (Array.isArray(value) || (typeof value === 'object' && value !== null)) {
        filtered[key] = value
      }
    })
    return filtered
  })

  // 获取变量类型标签
  const getVariableTypeLabel = (value) => {
    if (Array.isArray(value)) {
      return `数组[${value.length}]`
    }
    if (typeof value === 'object' && value !== null) {
      return `对象{${Object.keys(value).length}}`
    }
    return typeof value
  }

  // 格式化变量数据用于预览
  const formatVariableData = computed(() => {
    const variable = localConfig.value.dataBinding?.variable
    if (!variable || !props.variables[variable]) {
      return '无数据'
    }

    const data = props.variables[variable]
    if (Array.isArray(data)) {
      return JSON.stringify(data.slice(0, 3), null, 2) + (data.length > 3 ? '\n...' : '')
    }
    return JSON.stringify(data, null, 2)
  })

  // 获取当前选中变量的数据
  const selectedVariableData = computed(() => {
    const variable = localConfig.value.dataBinding?.variable
    if (!variable || !props.variables[variable]) {
      return null
    }
    return props.variables[variable]
  })

  // 获取可用字段列表
  const availableFields = computed(() => {
    if (!selectedVariableData.value || !Array.isArray(selectedVariableData.value)) {
      return []
    }

    // 从数组的第一个对象中提取字段名
    const firstItem = selectedVariableData.value[0]
    if (!firstItem || typeof firstItem !== 'object') {
      return []
    }

    return Object.keys(firstItem)
  })

  // 处理绑定开关
  const handleBindingToggle = (enabled) => {
    if (enabled) {
      emit('update-property', 'config.dataBinding', {
        enabled: true,
        variable: '',
        mapping: {
          xField: '',
          yField: '',
          seriesField: ''
        }
      })
    } else {
      emit('update-property', 'config.dataBinding', {
        enabled: false
      })
    }
  }

  // 处理变量选择
  const handleVariableChange = (variable) => {
    emit('update-property', 'config.dataBinding.variable', variable)
  }

  // 处理映射配置
  const handleMappingChange = (field, value) => {
    emit('update-property', `config.dataBinding.mapping.${field}`, value)
  }

  // 自动检测字段映射
  const autoDetectMapping = () => {
    if (!selectedVariableData.value || !Array.isArray(selectedVariableData.value)) {
      return
    }

    const firstItem = selectedVariableData.value[0]
    if (!firstItem || typeof firstItem !== 'object') {
      return
    }

    const fields = Object.keys(firstItem)
    const mapping = {}

    // 智能推荐字段映射
    // X轴字段：优先选择包含 name, label, category, date, month 等的字段
    const xAxisCandidates = ['name', 'label', 'category', 'date', 'month', 'time', 'period']
    const xField = fields.find(field =>
      xAxisCandidates.some(candidate =>
        field.toLowerCase().includes(candidate.toLowerCase())
      )
    ) || fields[0]

    // Y轴字段：优先选择包含 value, amount, count, sales, profit 等的字段
    const yAxisCandidates = ['value', 'amount', 'count', 'sales', 'profit', 'revenue', 'total', 'num', 'quantity']
    const yField = fields.find(field =>
      yAxisCandidates.some(candidate =>
        field.toLowerCase().includes(candidate.toLowerCase())
      )
    ) || fields.find(field => field !== xField && typeof firstItem[field] === 'number')

    // 系列字段：优先选择包含 type, series, region, category 等的字段
    const seriesCandidates = ['type', 'series', 'region', 'category', 'group', 'class']
    const seriesField = fields.find(field =>
      field !== xField && field !== yField &&
      seriesCandidates.some(candidate =>
        field.toLowerCase().includes(candidate.toLowerCase())
      )
    )

    // 应用检测到的映射
    if (xField) {
      emit('update-property', 'config.dataBinding.mapping.xField', xField)
    }
    if (yField) {
      emit('update-property', 'config.dataBinding.mapping.yField', yField)
    }
    if (seriesField) {
      emit('update-property', 'config.dataBinding.mapping.seriesField', seriesField)
    }
  }
</script>

<style scoped>
  .variable-binding {
    border-top: 1px solid #e4e7ed;
    padding-top: 16px;
    margin-top: 16px;
  }

  .variable-option {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .variable-name {
    font-family: 'Monaco', 'Courier New', monospace;
    font-size: 13px;
  }

  .variable-type {
    font-size: 12px;
    color: #909399;
    background: #f0f2f5;
    padding: 2px 6px;
    border-radius: 4px;
  }

  .mapping-config {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .quick-config {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
    padding: 8px;
    background: #f8f9fa;
    border-radius: 4px;
  }

  .mapping-item {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .mapping-item label {
    min-width: 70px;
    font-size: 12px;
    color: #606266;
  }

  .data-preview {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .preview-content {
    max-height: 200px;
    overflow-y: auto;
    background: #f5f7fa;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    padding: 8px;
  }

  .preview-content pre {
    margin: 0;
    font-size: 12px;
    color: #606266;
    white-space: pre-wrap;
    word-break: break-all;
  }

  .data-info {
    padding: 8px;
    background: #f0f9ff;
    border: 1px solid #b3d8ff;
    border-radius: 4px;
  }
</style>
