// 示例：层级嵌套的schema结构
export const hierarchicalSchemaExample = {
  canvas: {
    width: 1920,
    height: 1080
  },
  version: '2.0',
  components: [
    {
      id: 'container-1',
      type: 'div-container',
      name: '主容器',
      position: { x: 50, y: 50 },
      size: { width: 800, height: 600 },
      config: {
        backgroundColor: '#ffffff',
        borderColor: '#e4e7ed',
        borderWidth: 1,
        borderStyle: 'solid',
        borderRadius: 4,
        padding: 16,
        showTitleBar: true,
        titleText: '主容器',
        titleHeight: 32,
        titleBackgroundColor: '#f5f5f5',
        titleTextColor: '#303133'
      },
      children: [
        {
          id: 'container-1-left',
          type: 'div-container',
          name: '左侧容器',
          position: { x: 66, y: 98 },
          size: { width: 384, height: 536 },
          config: {
            backgroundColor: '#f8f9fa',
            borderColor: '#e4e7ed',
            borderWidth: 1,
            borderStyle: 'solid',
            borderRadius: 4,
            padding: 8
          },
          children: [
            {
              id: 'chart-1',
              type: 'pie-chart',
              name: '饼图',
              position: { x: 74, y: 106 },
              size: { width: 368, height: 250 },
              config: {
                title: '销售数据饼图',
                showTitle: true,
                showLegend: true,
                showTooltip: true,
                colors: {
                  theme: 'default',
                  customColors: ['#5470c6', '#91cc75', '#fac858', '#ee6666']
                }
              }
            }
          ]
        },
        {
          id: 'container-1-right',
          type: 'div-container',
          name: '右侧容器',
          position: { x: 458, y: 98 },
          size: { width: 384, height: 536 },
          config: {
            backgroundColor: '#f8f9fa',
            borderColor: '#e4e7ed',
            borderWidth: 1,
            borderStyle: 'solid',
            borderRadius: 4,
            padding: 8
          },
          children: [
            {
              id: 'chart-2',
              type: 'bar-chart',
              name: '柱状图',
              position: { x: 466, y: 106 },
              size: { width: 368, height: 250 },
              config: {
                title: '月度销售柱状图',
                showTitle: true,
                showLegend: true,
                showTooltip: true,
                colors: {
                  theme: 'default',
                  customColors: ['#5470c6']
                },
                barConfig: {
                  type: 'basic',
                  barWidth: 20,
                  showValue: true
                }
              }
            },
            {
              id: 'chart-3',
              type: 'line-chart',
              name: '折线图',
              position: { x: 466, y: 366 },
              size: { width: 368, height: 250 },
              config: {
                title: '趋势折线图',
                showTitle: true,
                showLegend: true,
                showTooltip: true,
                colors: {
                  theme: 'default',
                  customColors: ['#91cc75']
                }
              }
            }
          ]
        }
      ]
    },
    {
      id: 'standalone-chart',
      type: 'bar-chart',
      name: '独立图表',
      position: { x: 900, y: 50 },
      size: { width: 400, height: 300 },
      config: {
        title: '独立柱状图',
        showTitle: true,
        showLegend: true,
        showTooltip: true,
        colors: {
          theme: 'default',
          customColors: ['#fac858']
        }
      },
      children: []
    }
  ]
}

// 测试转换功能的函数
export function testSchemaTransformation() {
  import('./schemaTransform.js').then(({ transformToFlatSchema }) => {
    console.log('=== 层级Schema示例 ===')
    console.log(JSON.stringify(hierarchicalSchemaExample, null, 2))

    console.log('\n=== 转换为平铺Schema ===')
    const flatResult = transformToFlatSchema(
      hierarchicalSchemaExample.components
    )
    console.log(
      JSON.stringify(
        { ...hierarchicalSchemaExample, components: flatResult },
        null,
        2
      )
    )
  })
}
